/**
 * @file portal-error.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { NextResponse } from "next/server";
import Logger from "./logger";
import { PCSD_ERROR_MESSAGES } from "./definitions";

/**
 * ポータルエラークラス
 */
class PortalError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "PortalError";
  }
}

export default PortalError;

// Prismaエラーかどうかを判定する関数
export function isPrismaError(error: Error) {
  return error.name.includes("PrismaClient");
}

// APIエラーをハンドリングする関数
export function handleApiError(error: any) {
  // エラーログを出力
  Logger.error({ message: error.message, stack: error.stack });

  // Prismaエラーの場合はエラーレスポンスを返す
  if (isPrismaError(error)) {
    return NextResponse.json(
      { error: PCSD_ERROR_MESSAGES.EMEC0006 },
      { status: 500 },
    );
  } else {
    // それ以外のエラーの場合もエラーレスポンスを返す
    return NextResponse.json(
      { error: PCSD_ERROR_MESSAGES.EMEC0018 },
      { status: 500 },
    );
  }
}

// サーバーエラーをハンドリングする関数
export function handleServerError(error: any) {
  // エラーログを出力
  Logger.error({ message: error.message, stack: error.stack });

  // Prismaエラーの場合はエラーをスロー
  if (isPrismaError(error)) {
    throw new Error(PCSD_ERROR_MESSAGES.EMEC0006);
  } else {
    // それ以外のエラーの場合もエラーをスロー
    throw new Error(PCSD_ERROR_MESSAGES.EMEC0018);
  }
}
