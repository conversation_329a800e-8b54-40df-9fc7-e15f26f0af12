/**
 * @file route.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import ServerData from '@/app/lib/data';
import {
  allowedUploadCalendarFileNames, AS_TAR_COMMAND_PATH, AS_TAR_FILE_PREFIX,
  UPLOAD_PATH,
  UPLOAD_TAR_PATH, AS_TAR_COMMAND
} from "@/app/lib/definitions";
import Logger from '@/app/lib/logger';
import { handleApiError } from "@/app/lib/portal-error";
import { formatDateToYYYYMMDDHHMMSS } from "@/app/lib/utils";
import { execSync } from 'child_process';
import { XMLValidator } from "fast-xml-parser";
import fs from 'fs';
import { NextRequest, NextResponse } from 'next/server';
import path from 'path';


// ファイルチェック結果
interface FileCheckResult {
  isFormatError: boolean,
  isToolSizeError: boolean,
  isOTWWTSizeError: boolean,
  isOTWSizeError: boolean,
}

// ファイルチェック結果初期化
function createDefaultFCResult(): FileCheckResult {
  return {
    isFormatError: false,
    isToolSizeError: false,
    isOTWWTSizeError: false,
    isOTWSizeError: false,
  };
}

export async function POST(request: NextRequest) {

  // カレンダーツール名前
  let toolName: string = '';
  // 圧縮ファイル名前
  let outputTarFile: string = '';

  try {
    const formData = await request.formData();

    // カレンダーグループ名
    const groupName = formData.get('grpName') as string;
    //　カレンダーID
    const id = formData.get('id') as string;
    // アップロードファイル
    const files = formData.getAll('files');

    // 複数カレンダーツール存在かどうかフラグ
    let hasToolFile: boolean = false;

    // アップロードファイルの格納フォルダー
    const dirPath = path.join(process.cwd(), UPLOAD_PATH);
    // アップロードファイル格納フォルダーが存在がない場合、作成する
    await fs.promises.mkdir(dirPath, { recursive: true });

    // ファイルチェック結果格納配列の初期化
    const fileCheckResults: FileCheckResult[] = [];
    for (let index = 0; index < 5; index++) {
      fileCheckResults.push(createDefaultFCResult());
    }

    // OTWWTCAL.xml、OTWCALEX01.xml～OTWCALEX04.xmlのファイル名前を小文字に変換して新しい配列を作成
    const allowedUploadCalendarFileNamesLower = allowedUploadCalendarFileNames.map(fileName => fileName.toLocaleLowerCase());

    // OTWCALEX01.xml～OTWCALEX04.xmlのファイル名前
    const otwElementsToCheck = allowedUploadCalendarFileNamesLower.slice(1, 5);

    // アップロードファイルのチェックと格納
    let index = -1;
    let isCheckError = false;
    const filePaths = [];
    let xlsmFileExisted = false;
    const fileErrors = [];

    for (const file of files) {
      index++;
      if (file instanceof File) {
        if (file.name.toLowerCase().endsWith('.xlsm') && xlsmFileExisted) {
          fileErrors.push(true);
          // xlsm形式のファイルが初めて出現した場合、フラグを更新してエラーなし
        } else if (file.name.toLowerCase().endsWith('.xlsm')) {
          xlsmFileExisted = true;
          fileErrors.push(false);
          // その他のファイルについて、許可されたファイル名リストに存在しない場合はエラーとする
        } else {
          if (!isCheckError) {
            isCheckError = !allowedUploadCalendarFileNames.includes(file.name);
          } else {
            // ファイルの検証に失敗しました。FLAGに値を割り当てる必要はありません
          }
          // ファイル名が不正かどうかをチェック
          fileErrors.push(!allowedUploadCalendarFileNames.includes(file.name));
        }

        // カレンダーツール名前の格納
        if (file.name.toLowerCase().endsWith('.xlsm')) {
          // カレンダーツールが複数で存在しているかどうかを確認する。
          if (hasToolFile) {
            fileCheckResults[index] = { isFormatError: true, isToolSizeError: false, isOTWSizeError: false, isOTWWTSizeError: false };
            isCheckError = true;
            // エラー発生、次ファイルをチェックする
            continue;
          } else {
            toolName = file.name;
            // ツールがある場合、フラグは「true」を設定して、後で複数カレンダーツール存在かどうか判断用
            hasToolFile = true;
            fileCheckResults[index] = { isFormatError: false, isToolSizeError: false, isOTWSizeError: false, isOTWWTSizeError: false };
          }
        } else {
          // 許可されたファイル名リストに存在しない場合はエラーとする
          if (!allowedUploadCalendarFileNamesLower.includes(file.name.toLowerCase())) {
            fileCheckResults[index] = { isFormatError: true, isToolSizeError: false, isOTWSizeError: false, isOTWWTSizeError: false };
            isCheckError = true;
            // エラー発生、次ファイルをチェックする
            continue;
          } else {
            // エラーがない場合、特に処理を行いません。
          }
        }

        // ファイルサイズチェック
        if (file.name.toLowerCase().endsWith('.xlsm') && file.size > 10485760) {
          // カレンダーツールが31175バイトをお超える場合
          fileCheckResults[index] = { isFormatError: false, isToolSizeError: true, isOTWSizeError: false, isOTWWTSizeError: false };
          isCheckError = true;
          continue;
        }
        // OTWWTCAL.xml、かつ、ファイルサイズが31175バイトをお超える場合、falseを返す
        else if (file.name.toLowerCase() === allowedUploadCalendarFileNamesLower[0] && file.size > 31175) {
          fileCheckResults[index] = { isFormatError: false, isToolSizeError: false, isOTWSizeError: false, isOTWWTSizeError: true };
          isCheckError = true;
          continue;
        }
        // OTWCALEX01.xml～OTWCALEX04.xml、かつ、ファイルサイズが31175バイトをお超える場合、falseを返す
        else if (otwElementsToCheck.includes(file.name.toLowerCase()) && file.size > 31127) {
          fileCheckResults[index] = { isFormatError: false, isToolSizeError: false, isOTWSizeError: true, isOTWWTSizeError: false };
          isCheckError = true;
          continue;
        } else {
          // エラーがない場合、特に処理を行いません。
        }

        // アップロードファイルの格納
        const filePath = path.join(dirPath, file.name);
        await fs.promises.writeFile(filePath, Buffer.from(await file.arrayBuffer()));
        filePaths.push(filePath);

        // xmlフォーマットチェック
        if (file.name.toLowerCase().endsWith('xml')) {
          // ファイルパスからXMLファイルを読み取り、文字列として取得
          const xmlContent = await fs.promises.readFile(filePath, 'utf-8');
          // XMLValidatorを使用してXML内容を検証する
          const validation = XMLValidator.validate(xmlContent);

          // 検証結果がtrueでない場合（XMLフォーマットエラーがある場合）
          if (validation !== true) {
            isCheckError = true;
            // fileCheckResultsにフォーマットエラーを記録
            fileCheckResults[index] = { isFormatError: true, isToolSizeError: false, isOTWSizeError: false, isOTWWTSizeError: false };
          } else {
            // 検証に成功した場合、fileCheckResultsにエラー無しを記録
            fileCheckResults[index] = { isFormatError: false, isToolSizeError: false, isOTWSizeError: false, isOTWWTSizeError: false };
          }
        } else {
          // xml以外のファイル場合、フォーマットチェック行いません。
        }
      }
    }

    // チェックエラー発生場合
    if (isCheckError) {
      // アプロードファイル削除
      for (const filePath of filePaths) {
        await fs.promises.unlink(filePath);
      }

      return NextResponse.json({ success: false, message: fileCheckResults, fileErrors: fileErrors }, { status: 422 });
    } else {
      // チェックエラーがない場合、特に処理を行いません。
    }

    // ファイルを圧縮する
    // 圧縮したいファイルの名前
    let xmlfiles: string[] = [
      allowedUploadCalendarFileNames[1],
      allowedUploadCalendarFileNames[2],
      allowedUploadCalendarFileNames[3],
      allowedUploadCalendarFileNames[4],
    ];

    // 圧縮するファイルの格納フォルダー
    const outputPath = path.join(process.cwd(), UPLOAD_PATH, UPLOAD_TAR_PATH);
    await fs.promises.mkdir(outputPath, { recursive: true });

    // 圧縮ファイル名前
    outputTarFile = path.join(outputPath, AS_TAR_FILE_PREFIX + '_' + formatDateToYYYYMMDDHHMMSS(new Date()));

    // 圧縮ファイルコマンド（as_tar）を作成する
    const projectRoot = process.cwd();
    const exePath = path.join(projectRoot, AS_TAR_COMMAND_PATH, AS_TAR_COMMAND);
    const commandStr = `${exePath} -c \
    ${outputTarFile} \
    ${path.join(dirPath, allowedUploadCalendarFileNames[1])} \
    ${path.join(dirPath, allowedUploadCalendarFileNames[2])} \
    ${path.join(dirPath, allowedUploadCalendarFileNames[3])} \
    ${path.join(dirPath, allowedUploadCalendarFileNames[4])}`;

    // 「AS_TAR」を行う、ファイルを圧縮する
    execSync(commandStr);

    // DB格納用な特定日カレンダー定義(01～04)の作成
    const extCalARContents = fs.readFileSync(outputTarFile);

    //「カレンダー定義(XML)データ」と「休日カレンダー定義(XML)データ」を作成する
    const owtwtCalName = path.join(dirPath, allowedUploadCalendarFileNames[0]);       // 就業時間カレンダーファイル名（OTWWTCAL.xml）
    const timeCalContents = fs.readFileSync(owtwtCalName);                            // カレンダー定義(XML)データ
    const holidayCalContents = Buffer.from([]);                       // 休日カレンダー定義(XML)データ(今はもう利用しない)

    // 「カレンダー作成ツールファイル名」と「カレンダー作成ツールデータ」を作成する
    const calendarToolName = toolName;      // カレンダー作成ツールファイル名
    // カレンダー作成ツールデータ取得
    const calendrafilePath = path.join(dirPath, calendarToolName);
    const calendarToolContents = fs.readFileSync(calendrafilePath);

    // カレンダーグループ情報の新規と変更
    const updateResult = await ServerData.insertOrUpdateCalendar(id, timeCalContents, extCalARContents, calendarToolName, calendarToolContents, groupName, holidayCalContents);
    // アップロードファイル削除
    delUploadedFiles(toolName, outputTarFile);

    if (updateResult === 0) {
      const errorInfo = `カレンダー[${id}]が存在しない。`
      Logger.info({
        message: errorInfo,
      });
      return NextResponse.json({ message: errorInfo }, { status: 400 });
    }
    else {
      return NextResponse.json({ status: 200 });
    }
  } catch (error) {
    delUploadedFiles(toolName, outputTarFile);
    return handleApiError(error);
  }
}

// アップロードファイル削除
function delUploadedFiles(toolName: string, outputTarFile: string) {
  // アップロードファイルの格納フォルダー
  const dirPath = path.join(process.cwd(), UPLOAD_PATH);
  const calendarToolName = toolName;      // カレンダー作成ツールファイル名
  // アップロードファイル削除
  const calendrafilePath = path.join(dirPath, calendarToolName);
  safeUnlink(calendrafilePath);

  const owtwtCalName = path.join(dirPath, allowedUploadCalendarFileNames[0]);       // 就業時間カレンダーファイル名（OTWWTCAL.xml）
  safeUnlink(owtwtCalName);

  safeUnlink(path.join(dirPath, allowedUploadCalendarFileNames[1]));
  safeUnlink(path.join(dirPath, allowedUploadCalendarFileNames[2]));
  safeUnlink(path.join(dirPath, allowedUploadCalendarFileNames[3]));
  safeUnlink(path.join(dirPath, allowedUploadCalendarFileNames[4]));
  // tarファイル削除
  safeUnlink(outputTarFile);
}

// ファイル存在しれば、削除する
function safeUnlink(file: string) {
  if (fs.existsSync(file)) {
    fs.unlinkSync(file);
  }
}
