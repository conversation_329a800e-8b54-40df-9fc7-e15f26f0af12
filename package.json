{"private": true, "author": {"name": "WSST"}, "version": "2.0.0", "license": "Copyright (C) 2025, Hitachi Solutions, Ltd.", "scripts": {"dev": "prisma generate && next dev -H 0.0.0.0", "build": "prisma generate && next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@azure/storage-blob": "^12.24.0", "@prisma/client": "^5.19.1", "@types/react": "^18.3.7", "archiver": "^7.0.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "fast-xml-parser": "^4.5.0", "flowbite": "^2.5.1", "iron-session": "^8.0.3", "isomorphic-dompurify": "^2.22.0", "jsonwebtoken": "^9.0.2", "next": "^14.2.12", "p-limit": "^6.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "sanitize-html": "^2.15.0", "swr": "^2.2.5", "uuid": "^11.1.0", "winston": "^3.14.2"}, "devDependencies": {"@types/archiver": "^6.0.2", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.5.5", "@types/react": "18.0.28", "@types/sanitize-html": "^2.15.0", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.20", "eslint": "9.16.0", "eslint-config-next": "15.0.4", "postcss": "^8.4.47", "prettier": "^3.3.3", "prettier-plugin-organize-attributes": "^1.0.0", "prettier-plugin-organize-imports": "^4.0.0", "prettier-plugin-tailwindcss": "^0.6.6", "prisma": "^5.19.1", "tailwindcss": "^3.4.12", "ts-node": "^10.9.2", "typescript": "^5.6.2"}, "prettier": {"printWidth": 80}}