import React, { useState } from 'react';
import Image from 'next/image';
import clsx from 'clsx';

interface PasswordInputProps {
  id: string,
  value?: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  isError?: boolean;
}

const PasswordInput: React.FC<PasswordInputProps> = ({ id = '', value = '', onChange, placeholder = 'パスワード入力', isError = false }) => {
  const [inputType, setInputType] = useState<'password' | 'text'>('password');

  const togglePasswordVisibility = () => {
    setInputType(inputType === 'password' ? 'text' : 'password');
  };

  return (
    <div className="relative w-64 mr-6">
      <input
        type={inputType}
        id={id}
        name={id}
        className={clsx(
          "w-full ml-4 custom-password custom-placeholder rounded border-gray-300 border bg-gray-50 p-2 text-sm text-gray-900 focus:border-gray-300 focus:ring-gray-300",
          {
            "border-red-500": isError,
          },
        )}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        maxLength={10}
      />

      <span
        className="toggle-password"
        onClick={togglePasswordVisibility}
        style={{
          position: 'absolute',
          right: '10px',
          top: '50%',
          transform: 'translateY(-50%)',
          cursor: 'pointer',
          userSelect: 'none'
        }}
      >
        {value ? (
          inputType === 'password' ? (
            <Image
              src="/icons/eye.png"
              width={23}
              height={23}
              alt=""
            />
          ) : (
            <Image
              src="/icons/invisible.png"
              width={23}
              height={23}
              alt=""
            />
          )
        ) : null}
      </span>

      <style jsx>{`
        .custom-password::-ms-reveal {
          display: none; /* Hide the default eye icon in Edge */
        }
        
        .custom-placeholder::placeholder {
          font-size: 12px;
          color: #888; 
          font-style: italic; 
        }
      `}</style>
    </div>
  );
};

export default PasswordInput;
