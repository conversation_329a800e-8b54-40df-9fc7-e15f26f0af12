/**
 * @file login-form.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

"use client";

import clsx from "clsx";
import { useRouter } from "next/navigation"; // Next.jsのルーター
import { useEffect, useState } from "react"; // リアクトコンポーネント用のライブラリ
import { PCSD_ERROR_MESSAGES } from "../lib/definitions";
import Spinner from "./spinner";

// ログインフォームコンポーネント
export default function LoginForm() {
  const [loading, setLoading] = useState(false); // ローディング状態の管理
  const [error, setError] = useState<string>(""); // エラーメッセージの管理
  const router = useRouter(); // ルーターのインスタンス取得

  useEffect(() => {
    const fetchError = async () => {
      const redirectUrl = window.location.href;
      if (redirectUrl.includes("error07")) {
        setError(PCSD_ERROR_MESSAGES.EMEC0018);
        return;
      }
    };
    fetchError();

    const backdrop = document.querySelector(".modal-backdrop");
    if (backdrop) {
      backdrop.remove();
    } else {
      // 特に処理がない
    }
  }, []);

  // ログイン処理の関数
  const login = async () => {
    try {
      setLoading(true); // ローディング状態をtrueに設定
      // keycloakログイン画面URL取得
      const response = await fetch(`/api/login`, {
        method: "POST", // POSTリクエスト
        headers: {
          "Content-Type": "application/json", // JSON形式のヘッダー
        }
      });
      // レスポンスからJSONデータ(Keycloak認証URL)を取得する
      const data = await response.json();
      if (data.status === 200) {
        // KEYCLAOKログイン画面が開きます
        router.push(data.url);
        return;
      } else {
        // keycloakログインページのURLが正しくありません
        setError(PCSD_ERROR_MESSAGES.EMEC0018);
      }
    } catch (_) {
      setError(PCSD_ERROR_MESSAGES.EMEC0018);
    } finally {
      setLoading(false); // ローディング状態をfalseに設定
    }
  };

  return (
    <form className="flex max-w-md flex-col gap-2 lg:gap-2 2xl:gap-4 ">
      <h1 className="text-xl lg:text-xl 2xl:text-3xl font-semibold">PC自動シャットダウンサービス</h1>
      <h1 className="mb-0 lg:mb-2 2xl:mb-4 text-lg lg:text-lg 2xl:text-2xl font-medium"> </h1>

      <div className="flex items-center justify-between">
        <div className="w-28">
          <button type="button"
            onClick={login}
            className="rounded w-28 bg-gradient-light shadow-light drop-shadow-light py-2 w-full text-center text-xs font-medium text-transparent text-gray-900 hover:opacity-80 transition duration-3 00"
            style={{ backgroundImage: 'url(/icons/btn_login.ico)', }}
          >
            <Spinner
              className={clsx("inline-block mr-2", { hidden: !loading })}
            />
            ログイン
            &nbsp;
          </button>
        </div>
      </div>
      <div
        className={clsx(
          "h-20 transform-gpu rounded bg-[#F3F4F6]/50 p-4 text-gray-900",
          { invisible: !error },
        )}
      >
        <div className="mb-2 text-sm lg:text-sm 2xl:text-base font-semibold">
          <img
            src="/dialogerror_32.png"
            className="mr-2 inline-block h-4 w-4"
            alt="error"
          />
          エラー
        </div>
        <div className="text-xs lg:text-xs 2xl:text-sm font-normal">{error}</div>
      </div>
    </form>
  );
}
