{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"@/components/*": ["components/*"], "@/pages/*": ["pages/*"], "@/app/*": ["app/*"], "@/lib/*": ["lib/*"], "@/hooks/*": ["hooks/*"], "@/styles/*": ["styles/*"], "@/cypress/*": ["cypress/*"]}, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "experimentalDecorators": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "types/**/*.ts"], "exclude": ["node_modules"]}