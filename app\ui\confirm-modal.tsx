/**
 * @file confirm-modal.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

"use client";

import { ModalProps } from "../lib/definitions";

interface ConfirmModalProps extends ModalProps {
  message?: string | null;
  onAction: () => void;
  onClose: () => void;
}

// パスワード変更ダイアログコンポーネント
export default function ConfirmModal({
  message,
  onAction,
  onClose,
}: ConfirmModalProps) {
  return (
    <>
      <div className="fixed inset-0 bg-gray-900 opacity-50 z-40"></div>
      <div
        id="confirm-modal"
        tabIndex={-1}
        className="flex overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full"
      >
        <div className="relative w-full max-w-lg max-h-full">
          <form className="relative rounded shadow bg-gray-600">
            <div className="flex items-center justify-between p-4 border-b rounded-t bg-gradient-header">
              <h3 className="text-lg font-semibold text-white">
                確認メッセージ
              </h3>
              <button
                type="button"
                className="text-gray-400 bg-transparent rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center hover:bg-gray-600 hover:text-white"
                onClick={onClose}
              >
                <svg
                  className="w-3 h-3"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 14 14"
                >
                  <path
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                  />
                </svg>
                <span className="sr-only">Close modal</span>
              </button>
            </div>
            <div className="h-32 px-8 py-4 space-y-4 bg-white text-base font-medium">
              <div className="text-base font-normal whitespace-pre-line">
                {message}
              </div>
            </div>
            <div className="items-center justify-between p-4 border-t rounded-b bg-gradient-header">
              <div className="flex justify-end items-center">
                <button
                  type="button"
                  onClick={onAction}
                  className="w-28 rounded bg-gradient-dark px-3 py-2 text-center text-xs font-medium text-transparent 
              drop-shadow-dark shadow-dark hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
                  style={{ backgroundImage: "url(/icons/btn_ok.ico)" }}
                >
                  OK
                </button>
                <button
                  onClick={onClose}
                  type="button"
                  className="w-28 ms-3 rounded bg-gradient-blue px-3 py-2 text-center text-xs font-medium text-transparent 
              drop-shadow-blue shadow-inner hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
                  style={{ backgroundImage: "url(/icons/btn_cancel.ico)" }}
                >
                  キャンセル
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </>
  );
}
