/**
 * @file use-session.ts
 * @description セッションデータを管理するカスタムフックを定義するファイル
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import useSWR from "swr";
import { SessionData, defaultSession } from "@/app/lib/session";

// セッションAPIのルート
const sessionApiRoute = "/api/session";

/**
 * JSONデータをフェッチする非同期関数
 * @param input - リクエスト先のURL
 * @param init - fetchのオプション
 * @returns フェッチしたJSONデータ
 */
async function fetchJson<JSON = unknown>(
  input: RequestInfo,
  init?: RequestInit,
): Promise<JSON> {
  return fetch(input, {
    headers: {
      accept: "application/json",
      "content-type": "application/json",
    },
    ...init,
  }).then((res) => res.json());
}

/**
 * セッションデータを管理するカスタムフック
 * @returns セッションデータとローディング状態
 */
export default function useSession() {
  const { data: session, isLoading, mutate: getSession } = useSWR(
    sessionApiRoute,
    fetchJson<SessionData>,
    {
      fallbackData: defaultSession,
    },
  );

  return { session, isLoading, getSession };
}