/**
 * @file route.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { NextRequest, NextResponse } from 'next/server';
import { handleApiError } from "@/app/lib/portal-error";
import ServerData from '@/app/lib/data';

export async function POST(request: NextRequest) {
    try {
        // リクエストからグループ名を取得
        const { grpName } = await request.json();

        // カレンダーグループ名により、カレンダーを取得するメソッド
        const calendar = await ServerData.getCalendarsByGrpname(grpName);

        // グループ情報が存在するか確認
        if (calendar && calendar.length > 0) {
            // グループが存在する場合、JSON形式で{exists: true}を返す
            return NextResponse.json({ exists: true });
        } else {
            // グループが存在しない場合、JSON形式で{exists: false}を返す
            return NextResponse.json({ exists: false });
        }
    } catch (error) {
        // エラーハンドリング関数でエラーを処理し、エラーメッセージを返す
        return handleApiError(error);
    }
}
