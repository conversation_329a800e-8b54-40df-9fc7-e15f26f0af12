/**
 * @file userlogs.ts
 * @description 利用者環境ログ機能のデータアクセス層
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

"use server";

import prisma from "@/app/lib/prisma";
import { LogFunctionSignature } from "@/app/lib/logger";
import { handleServerError } from "@/app/lib/portal-error";
import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";
import { formatDate } from "@/app/lib/utils";

/**
 * 大文字ソート条件を変換し、SQLのORDER BY句用の文字列を生成する。
 *
 * この関数は、指定されたソートフィールドとソート順序をもとに、
 * SQLのORDER BY句に使用できる文字列を生成します。
 * 特に、指定されたフィールドが存在するかどうかを基準にソートを実現します。
 * また、ソートフィールドが第2ソートキーと異なる場合は、
 * 指定された第2ソートキーを追加します。
 *
 * @param sort - ソートするフィールド名
 * @param order - ソート順序（'asc'または'desc'）
 * @param secondSortKeyUper - 第2ソートキー（例: 'SOFTWARE_NAME'）。ソート順序は'asc'で固定します。
 * @returns SQLのORDER BY句用の文字列
 *
 * @remarks
 * - ソートフィールド名と第2ソートキーは、内部的に大文字に変換されます。
 * - 生成されたORDER BY句は、大文字のフィールド名を使用します。
 */
function formatSortConditionsUperCase(
  sort: string,
  order: string,
  secondSortKeyUper: string,
): string {
  const sortUperStr = sort.toUpperCase();
  const orderLowerStr = order.toLowerCase();

  let orderByStr = "";

  if (
    sortUperStr === "LOG_TIME" ||
    sortUperStr === "STARTED_AT" ||
    sortUperStr === "COMPLETED_AT"
  ) {
    if (orderLowerStr === "desc") {
      orderByStr = `"${sortUperStr}" desc NULLS LAST`;
    } else {
      orderByStr = `"${sortUperStr}" asc NULLS FIRST`;
    }
  } else {
    orderByStr = `"${sortUperStr}" ${orderLowerStr}`;
  }

  // ソートフィールドが第2ソートキーでない場合は、指定された第2ソートキーを追加する
  if (sortUperStr !== secondSortKeyUper) {
    orderByStr += `, "${secondSortKeyUper}" asc`;
  }

  return orderByStr;
}

interface UserLog {
  USER_ID: string;
}

interface UserEnvLogRecord {
  USER_ID: string;
  LOG_TIME?: Date | null;
}

interface FormattedUserEnvLogRecord extends UserEnvLogRecord {
  LOG_TIME_YYYYMMDDHHMMSS: string;
}

/**
 * 利用者環境ログ機能のデータアクセスクラス
 */
class UserLogsData {
  /**
   * 利用者環境ログ情報（USER_ENV_LOG）を検索し、一覧を取得します。
   * @param schemaName スキーマ名
   * @param filter フィルタ条件（ユーザーID）
   * @param sort ソート対象の列名
   * @param order ソート順（"asc" | "desc"）
   * @param page 現在のページ番号
   * @param size 1ページあたりの件数
   * @returns ページネーションされたログ情報のリストと総ページ数を含むオブジェクト
   */
  @LogFunctionSignature()
  static async fetchUserlogs(
    schemaName: string,
    filter: string,
    sort: string,
    order: string,
    page: number,
    size: number,
  ): Promise<{ items: FormattedUserEnvLogRecord[]; totalPages: number } | undefined> {
    try {
      const session = await getIronSession<SessionData>(
        cookies(),
        sessionOptions,
      );

      // パラメータとWHERE句の条件を格納する配列を初期化
      const params: (string | number)[] = [];
      const whereConditions: string[] = [];

      // schemaNameの条件を追加 (必須)
      whereConditions.push(`"SCHEMA_NAME" = $${params.length + 1}`);
      params.push(schemaName);

      if (filter) {
        whereConditions.push(`"USER_ID" LIKE $${params.length + 1}`);
        params.push(`${filter}%`);
      }

      // 組み立てた条件を "AND" で連結してWHERE句を作成
      const whereClause =
        whereConditions.length > 0
          ? `WHERE ${whereConditions.join(" AND ")}`
          : "";

      // 1. 総件数を取得するためのクエリ
      const countQuery = `SELECT COUNT(*) FROM "USER_ENV_LOG" ${whereClause};`;

      // 2. 指定されたページのデータを取得するためのクエリ
      const orderByStr = formatSortConditionsUperCase(sort, order, "USER_ID");
      const paginationClause = `LIMIT $${params.length + 1} OFFSET $${params.length + 2
        }`; // LIMITとOFFSETのプレースホルダ

      const dataQuery = `
        SELECT
            "USER_ID",
            "LOG_TIME"
        FROM "USER_ENV_LOG"
        ${whereClause}
        ORDER BY ${orderByStr}
        ${paginationClause};
      `;

      // ページネーションのためのパラメータを追加
      const skip = (page - 1) * size;
      const dataParams = [...params, size, skip];

      // つのクエリを並列で実行
      const [totalCountResult, userEnvLogs] = await Promise.all([
        prisma.$queryRawUnsafe<{ count: string }[]>(countQuery, ...params),
        prisma.$queryRawUnsafe<UserEnvLogRecord[]>(dataQuery, ...dataParams),
      ]);

      // 総件数を取得（結果は文字列として返ってくるので数値に変換）
      const totalCount = parseInt(totalCountResult[0]?.count || "0", 10);

      // 総ページ数を計算
      const totalPages = Math.ceil(totalCount / size);

      // 取得したデータのLOG_TIMEをユーザーのタイムゾーンに合わせてフォーマット
      const formattedLogs = userEnvLogs.map((userEnvLog) => {
        return {
          ...userEnvLog,
          LOG_TIME_YYYYMMDDHHMMSS: userEnvLog.LOG_TIME
            ? formatDate(userEnvLog.LOG_TIME, session.user.tz)
            : "",
        };
      });

      // フロントエンドが必要とする形式でオブジェクトを返す
      return { items: formattedLogs, totalPages: totalPages };
    } catch (error) {
      handleServerError(error);
      return undefined;
    }
  }

  /**
   * ログ時間（LOG_TIME）が記録されている全ての利用者環境ログ情報（USER_ENV_LOG）から、ユーザーIDのリストを取得します。
   * @param schemaName スキーマ名
   * @returns ユーザーIDのリスト
   */
  @LogFunctionSignature()
  static async fetchUserhavelogs(schemaName: string): Promise<UserLog[] | undefined> {
    try {
      const userEnvLogs = await prisma.$queryRawUnsafe<UserLog[]>(
        `
          SELECT "USER_ID"
          FROM "USER_ENV_LOG"
          WHERE "SCHEMA_NAME" = $1 AND "LOG_TIME" IS NOT NULL;
        `,
        schemaName,
      );
      return userEnvLogs;
    } catch (error) {
      handleServerError(error);
      return undefined;
    }
  }

  /**
   * 指定されたスキーマ名で、ログ時間が記録されている利用者環境ログの総件数を取得します。
   * @param schemaName スキーマ名
   * @returns ログ時間が記録されている総件数
   */
  @LogFunctionSignature()
  static async fetchAvailableUserlogsCount(schemaName: string): Promise<number | undefined> {
    try {
      const result = await prisma.$queryRawUnsafe<{ count: string }[]>(
        `
          SELECT COUNT(*) as count
          FROM "USER_ENV_LOG"
          WHERE "SCHEMA_NAME" = $1 AND "LOG_TIME" IS NOT NULL;
        `,
        schemaName,
      );

      return parseInt(result[0]?.count || "0", 10);
    } catch (error) {
      handleServerError(error);
      return undefined;
    }
  }

  /**
   * 利用者環境ログ情報の総件数を取得します。
   * @param schemaName スキーマ名
   * @param filter フィルタ条件（ユーザーID）
   * @returns 総件数
   */
  @LogFunctionSignature()
  static async fetchUserlogsCount(schemaName: string, filter?: string): Promise<number | undefined> {
    try {
      let whereClause = `WHERE "SCHEMA_NAME" = $1`;
      const params: (string | number)[] = [schemaName];

      if (filter && filter.trim() !== "") {
        whereClause += ` AND "USER_ID" LIKE $${params.length + 1}`;
        params.push(`${filter}%`); // 前方一致検索
      }

      const countQuery = `
        SELECT COUNT(*) as count
        FROM "USER_ENV_LOG"
        ${whereClause};
      `;

      const result = await prisma.$queryRawUnsafe<{ count: string }[]>(
        countQuery,
        ...params,
      );

      return parseInt(result[0]?.count || "0", 10);
    } catch (error) {
      handleServerError(error);
      return undefined;
    }
  }

  /**
   * 指定されたユーザーIDリストに基づいて、利用者環境ログ情報を取得します。
   * @param schemaName スキーマ名
   * @param userIds ユーザーIDのリスト
   * @returns 該当するユーザーログのリスト
   */
  @LogFunctionSignature()
  static async fetchUserlogsByIds(schemaName: string, userIds: string[]): Promise<UserLog[] | undefined> {
    try {
      if (userIds.length === 0) {
        return [];
      }

      const userEnvLogs = await prisma.$queryRawUnsafe<UserLog[]>(
        `
          SELECT "USER_ID"
          FROM "USER_ENV_LOG"
          WHERE "SCHEMA_NAME" = $1 
            AND "USER_ID" = ANY($2::text[])
            AND "LOG_TIME" IS NOT NULL;
        `,
        schemaName,
        userIds,
      );
      return userEnvLogs;
    } catch (error) {
      handleServerError(error);
      return undefined;
    }
  }

  /**
   * 指定されたユーザーIDに対応するスキーマ名を取得します。
   * @param id 検索対象のユーザーID
   * @returns スキーマ名（文字列）。見つからない場合はnull。
   */
  @LogFunctionSignature()
  static async fetchUserlogById(id: string): Promise<string | null> {
    try {
      if (!id) return null;

      const result = await prisma.$queryRawUnsafe<{ SCHEMA_NAME: string }[]>(
        `
        SELECT "SCHEMA_NAME"
        FROM "USER_ENV_LOG"
        WHERE "USER_ID" = $1
        LIMIT 1; 
        `,
        id,
      );

      return result[0]?.SCHEMA_NAME ?? null; // 結果が存在すればスキーマ名を、なければnullを返す
    } catch (error) {
      handleServerError(error);
      return null; // エラー時もnullを返す
    }
  }

}

// "use server" 文件では async 関数のみエクスポート可能なため、
// UserLogsData クラスの静的メソッドを個別にエクスポート
export const fetchUserlogs = UserLogsData.fetchUserlogs;
export const fetchUserhavelogs = UserLogsData.fetchUserhavelogs;
export const fetchAvailableUserlogsCount = UserLogsData.fetchAvailableUserlogsCount;
export const fetchUserlogsCount = UserLogsData.fetchUserlogsCount;
export const fetchUserlogsByIds = UserLogsData.fetchUserlogsByIds;
export const fetchUserlogById = UserLogsData.fetchUserlogById;
