/**
 * @file download-button.tsx
 * @description 「チェック一括ダウンロード」と「全件一括ダウンロード」の機能を提供する再利用可能なボタンコンポーネント。
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */
"use client";

import { PCSD_ERROR_MESSAGES } from "@/app/lib/definitions";
import ConfirmModal from "@/app/ui/confirm-modal";
import FeedbackModal from "@/app/ui/feedback-modal";
import { useRouter } from "next/navigation";
import { FC, useState } from "react";

/**
 * DownloadButtonコンポーネントのPropsの型定義
 */
type DownloadButtonProps = {
  url: string; // APIリクエストを送信する先のURL
  zipFileName: string; // ダウンロードされるZIPファイルの推奨名（現在未使用）
  buttonText: string; // ボタンに表示されるテキスト
  params?: string[]; // 「チェック一括」で選択された項目のIDリスト
  allUserids?: string[]; // 「全件一括」で対象となる全ユーザーIDのリスト
};

/**
 * ログダウンロードを開始するための汎用ボタンコンポーネント。
 * Propsに応じて「チェック一括」または「全件一括」の動作を切り替える。
 */
const DownloadButton: FC<DownloadButtonProps> = function ({
  url,
  zipFileName, // 現在このPropsはコンポーネント内で使用されていません
  buttonText,
  params,
  allUserids,
}) {
  const router = useRouter();

  // --- 状態管理 (State Management) ---

  // 確認モーダル用の状態
  const [isConfirmModalVisible, setIsConfirmModalVisible] = useState(false);
  const [confirmMsg, setConfirmMsg] = useState<string | null>(null);

  // フィードバック（成功/エラー）モーダル用の状態
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [feedbackMsg, setFeedbackMsg] = useState<string | null>(null);
  const [feedbackError, setFeedbackError] = useState<string | null>(null);

  // --- イベントハンドラ (Event Handlers) ---

  /**
   * ダウンロードボタンがクリックされた時の処理。
   * 処理の種類（チェック/全件）を判断し、確認モーダルを表示する。
   */
  const handleDownloadLogs = async () => {
    // 「チェック一括」の場合、選択項目が0件なら警告を表示
    if (params) {
      if (params.length === 0) {
        showMessageModal(PCSD_ERROR_MESSAGES.EMEC0022);
        return;
      }
      setConfirmMsg(
        "選択された利用者環境ログをダウンロードします。よろしいですか？",
      );
      setIsConfirmModalVisible(true);
    }
    // 「全件一括」の場合
    else {
      if (!allUserids || allUserids.length === 0) {
        showMessageModal(PCSD_ERROR_MESSAGES.EMEC0021);
        return;
      }
      setConfirmMsg(
        "利用者環境ログをすべてダウンロードします。よろしいですか？",
      );
      setIsConfirmModalVisible(true);
    }
  };

  /**
   * 確認モーダルで「はい」が押された時の処理。
   * APIにタスク作成リクエストを送信する。
   */
  const handleConfirmModalActionNew = async () => {
    setIsConfirmModalVisible(false); // まず確認モーダルを閉じる

    const hasParams = params && params.length > 0;
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        TASK_TYPE: hasParams ? "SELECTED" : "ALL",
        PARAMETERS: hasParams ? params : [],
      }),
    });

    // APIレスポンスのハンドリング
    if (!response.ok) {
      handleApiErrorResponse(response);
    } else {
      const data = await response.json();
      showMessageModal(data.message);
    }
  };

  /**
   * APIエラーレスポンスを共通で処理する
   * @param response フェッチAPIからのレスポンスオブジェクト
   */
  const handleApiErrorResponse = async (response: Response) => {
    if (response.status === 401) {
      router.push("/login");
    } else if (response.status === 404 || response.status === 500) {
      const data = await response.json();
      showErrorModal(data.error);
    } else {
      showErrorModal(PCSD_ERROR_MESSAGES.EMEC0018); // 予期せぬエラー
    }
  };

  /**
   * 確認モーダルで「いいえ」が押された時の処理（モーダルを閉じるだけ）。
   */
  const handleConfirmModalActionNewClose = () => {
    setIsConfirmModalVisible(false);
  };

  // --- モーダル表示用ヘルパー関数 ---

  /**
   * 成功メッセージ用のフィードバックモーダルを表示します。
   * @param newMessage 表示するメッセージ
   */
  const showMessageModal = (newMessage: string) => {
    setFeedbackMsg(newMessage);
    setFeedbackError(null);
    setIsModalVisible(true);
  };

  /**
   * エラーメッセージ用のフィードバックモーダルを表示します。
   * @param newError 表示するエラーメッセージ
   */
  const showErrorModal = (newError: string) => {
    setFeedbackError(newError);
    setFeedbackMsg(null);
    setIsModalVisible(true);
  };

  /**
   * フィードバックモーダルを閉じ、メッセージをリセットします。
   */
  const closeModal = () => {
    setIsModalVisible(false);
  };

  // --- レンダリング用ヘルパー関数 ---

  /**
   * ボタンのテキストに応じて適切なアイコン画像のパスを返します。
   * @param iconType ボタンのテキスト
   * @returns アイコン画像のURL
   */
  const getBackgroundImage = (iconType: string) => {
    return iconType === "チェック一括ダウンロード"
      ? "/icons/btn_checked_download.ico"
      : "/icons/btn_all_download.ico";
  };

  // --- レンダリング (Rendering) ---

  return (
    <>
      {/* 確認モーダル */}
      {isConfirmModalVisible && (
        <ConfirmModal
          message={confirmMsg}
          onAction={handleConfirmModalActionNew}
          onClose={handleConfirmModalActionNewClose}
        />
      )}

      {/* 成功・エラーフィードバックモーダル */}
      {isModalVisible && (
        <FeedbackModal
          message={feedbackMsg}
          error={feedbackError}
          onClose={closeModal}
        />
      )}

      {/* ダウンロードボタン本体 */}
      <button
        type="button"
        className="w-38 ms-3 rounded bg-gradient-blue px-3 py-2 text-center text-xs font-medium text-transparent 
      drop-shadow-blue shadow-inner hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
        onClick={handleDownloadLogs}
        style={{ backgroundImage: `url(${getBackgroundImage(buttonText)})` }}
      >
        {buttonText}
      </button>
    </>
  );
};

export default DownloadButton;
