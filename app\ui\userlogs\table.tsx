/**
 * @file table.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */
"use client";

import FeedbackModal from "@/app/ui/feedback-modal";
import { TableSkeleton } from "@/app/ui/skeletons";
import Tooltip from "@/app/ui/tooltip";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import Thead from "../thead";
import { checkForEmptyStringsAtPosition0 } from "@/app/lib/utils";

// 利用者環境ログテーブルコンポーネント
export default function UserlogsTable({
  blobInfos,
  hasBlobInfos,
  onSelectBlob,
  onClearBlob,
  onSelectAllBlob,
  filter,
  page,
  size,
}: {
  blobInfos: Array<[string, string[]]>;
  hasBlobInfos: boolean;
  onSelectBlob: (blobId: string, isSelected: boolean) => void;
  onClearBlob: () => void;
  onSelectAllBlob: (allBlobs: string[]) => void;
  filter: string;
  page: number;
  size: number;
}) {
  const router = useRouter();
  // ローディング状態を管理
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 利用者環境ログが存在し、かつデータがある場合にローディングを停止
    if (blobInfos && blobInfos.length > 0) {
      setLoading(false);
    } else if (!hasBlobInfos) {
      // 利用者環境ログが空または存在しない場合もローディングを停止
      setLoading(false);
    } else {
      // それ以外の場合はローディング状態を維持
      setLoading(true);
    }
    // 前に格納したユーザーIDをクリアする
    onClearBlob();
    // 選択された行をリセット
    setSelectedRows(new Set());
  }, [blobInfos, filter, page]);

  const [feedbackMsg, setFeedbackMsg] = useState<string | null>(null); // メッセージ用の状態
  const [feedbackError, setFeedbackError] = useState<string | null>(null); // エラー用の状態
  const [isModalVisible, setIsModalVisible] = useState(false); // モーダルの表示状態

  // モーダルを開きエラーを設定
  const showErrorModal = (newError: string) => {
    setFeedbackError(newError);
    setFeedbackMsg(null); // メッセージクリア
    setIsModalVisible(true); // モーダルを開く
  };
  // モーダルを閉じる
  const closeModal = () => {
    setIsModalVisible(false);
    setFeedbackError(null); // メッセージをクリア
    setFeedbackMsg(null); // エラーをクリア
  };

  // 指定ユーザーIDのファイルをダウンロード
  const handleDownloadOneLog = async (userid: string, setDownloadLoading: React.Dispatch<React.SetStateAction<boolean>>) => {
    setDownloadLoading(true);
    // サーバーにリクエストを送信してユーザーログをダウンロード
    const response = await fetch("/api/userlogs/" + userid + "/download", {
      method: "GET",
    });

    if (response.status === 401) {
      setDownloadLoading(false);
      router.push("/login");
      return;
    } else if (response.status === 404 || response.status === 500) {
      const data = await response.json();
      setDownloadLoading(false);
      showErrorModal(data.error);
      return;
    } else {
      const blob = await response.blob(); // Blobデータを取得
      setDownloadLoading(false); // ローディング状態を終了
      downloadFile(blob, `${userid}.zip`); // ダウンロード関数を呼び出す
    }
  };

  // 指定ユーザーIDのファイルをダウンロード
  const downloadFile = (blob: Blob, fileName: string) => {
    const url = window.URL.createObjectURL(blob); // BlobをURLに変換
    const a = document.createElement("a"); // ダウンロード用のリンクを作成
    a.href = url;
    a.download = fileName; // ダウンロードするファイル名を指定
    document.body.appendChild(a); // リンクをDOMに追加
    a.click(); // ダウンロードを実行
    a.remove(); // ダウンロード後にリンクを削除
  };

  // チェックボックスの選択状態を管理
  const handleCheckboxChange = (
    USER_ID: string,
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const isSelected = event.target.checked;
    // 選択された場合、選択された行をセットに追加
    if (isSelected) {
      setSelectedRows(selectedRows.add(USER_ID));
    } else {
      // 選択解除された場合、セットから削除
      selectedRows.delete(USER_ID);
      setSelectedRows(selectedRows);
    }
    onSelectBlob(USER_ID, isSelected);
  };

  const [downloadLoading, setDownloadLoading] = useState(false);
  const handleDownloadClick = (userid: string) => {
    handleDownloadOneLog(userid, setDownloadLoading);
  };

  const bounceAnimation = `
  @keyframes typing {
    from {
      width: 0;
    }
    to {
      width: 100%;
    }
  }
  @keyframes blink {
    from, to {
      border-color: transparent;
    }
    50% {
      border-color: black;
    }
  }
`;

  const styles = {
    container: {
      backgroundColor: "white",
      padding: "20px",
      borderRadius: "10px",
      textAlign: "center" as const,
      animation: "typing 2s steps(30, end), blink 0.75s step-end infinite",
    } as React.CSSProperties,
    modal: {
      position: "fixed",
      top: "0",
      left: "0",
      width: "100%",
      height: "100%",
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      zIndex: 1000,
    } as React.CSSProperties,
    modalContent: {
      backgroundColor: "white",
      padding: "20px",
      borderRadius: "10px",
      textAlign: "center",
    } as React.CSSProperties,
  };

  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
  const handleSelectAll = (selectAll: boolean) => {
    if (selectAll) {
      const enabledKeys = blobInfos
        .filter(([, info]) => info[0])
        .map(([key]) => key);
      onSelectAllBlob(enabledKeys);
      setSelectedRows(new Set(enabledKeys));
    } else {
      onSelectAllBlob([]);
      setSelectedRows(new Set());
    }
  };

  if (loading) {
    return <TableSkeleton />;
  } else {
    // 特に処理がない
  }

  return (
    <>
      {isModalVisible && (
        <FeedbackModal
          message={feedbackMsg}
          error={feedbackError}
          onClose={closeModal}
        />
      )}
      <table className="table-auto whitespace-nowrap w-full h-full text-left text-sm text-gray-500">
        <Thead
          headers={[
            { key: "no", label: "＃", css: "w-[60px]", isNotSort: true },
            {
              key: "chkbox",
              label: " ",
              css: "w-[60px]",
              chk: true,
              isChkDisable: checkForEmptyStringsAtPosition0(blobInfos),
            },
            { key: "USER_ID", label: "ユーザーID" },
            { key: "LOG_TIME", label: "最終更新日時" },
            { key: "download", label: "ダウンロード", isNotSort: true },
          ]}
          defaultOrder="LOG_TIME"
          defaultSort="desc"
          onSelectAll={handleSelectAll}
        />
        <tbody>
          {blobInfos?.length !== 0 ? (
            blobInfos.map((entry, index) => {
              const [key, value] = entry; // key: ユーザーID、value:最新更新時間
              return (
                <tr key={key} className="border-b odd:bg-white even:bg-gray-50">
                  <td className="w-[60px] overflow-hidden text-ellipsis whitespace-nowrap py-3 pl-6 pr-3 border-r">
                    {(page - 1) * size + index + 1}
                  </td>
                  <td className="w-[60px] overflow-hidden text-ellipsis whitespace-nowrap py-3 pl-6 pr-3 border-r">
                    <input
                      type="checkbox"
                      disabled={
                        value[0] === null || value[0] === "" ? true : false
                      }
                      style={
                        value[0] === null || value[0] === ""
                          ? {
                              backgroundColor: "#ddd",
                              borderColor: "#ccc",
                            }
                          : {}
                      }
                      checked={selectedRows.has(key)}
                      onChange={(e) => handleCheckboxChange(key, e)}
                    />
                  </td>
                  <td className="w-[620px] border-r px-6 py-4">
                    <Tooltip
                      text={key}
                      bigTip="big"
                      style={{ width: "10px", left: "30%" }}
                      torb={
                        index === size - 1 || index === blobInfos?.length - 1
                          ? "top"
                          : "bottom"
                      }
                    >
                      <p>{value[1]}</p>
                    </Tooltip>
                  </td>
                  <td className="border-r px-6 py-4">
                    <Tooltip
                      text={value[0]}
                      bigTip="big"
                      style={{ width: "10px", left: "30%" }}
                      torb={
                        index === size - 1 || index === blobInfos?.length - 1
                          ? "top"
                          : "bottom"
                      }
                    >
                      <p>{value[0]}</p>
                    </Tooltip>
                  </td>
                  <td className="whitespace-nowrap py-3 pl-6 pr-3 border-r">
                    <div className="flex items-center gap-3">
                      {value[0] === null || value[0] === "" ? null : (
                        <Tooltip
                          text={"ダウンロード"}
                          bigTip="big"
                          style={{ width: "10px", left: "30%" }}
                          torb={
                            index === size - 1 ||
                            index === blobInfos?.length - 1
                              ? "top"
                              : "bottom"
                          }
                        >
                          <Image
                            src="/icons/download.ico"
                            width={32}
                            height={32}
                            alt="download"
                            style={{ cursor: "pointer" }}
                            onClick={() => handleDownloadClick(key)}
                            tabIndex={0}
                          />
                        </Tooltip>
                      )}
                    </div>
                  </td>
                </tr>
              );
            })
          ) : (
            <tr>
              <td>
                <div className="p-4"></div>
              </td>
            </tr>
          )}
        </tbody>
      </table>
      {downloadLoading && (
        <div style={styles.modal}>
          <div style={styles.modalContent}>
            <h2>ファイルを圧縮中です。しばらくお待ちください...</h2>
          </div>
        </div>
      )}
      <style>{bounceAnimation}</style>
    </>
  );
}
