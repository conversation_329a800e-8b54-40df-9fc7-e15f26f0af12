/**
* @file call-back-form.tsx
* @description
* <AUTHOR>
* @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
*/

"use client";

import clsx from "clsx";
import { useRouter } from "next/navigation"; // Next.jsのルーター
import { useEffect, useState } from "react";
import Spinner from './spinner';

// URLから許可コードを抽出する
function extractCodeFromUrl(url: string) {
    const codeIndex = url.indexOf('code=');
    if (codeIndex !== -1) {
        const code = url.substring(codeIndex + 5);
        return code;
    } else {
        // 特に処理がない
    }
    return null;
}

// ログインフォームコンポーネント
export default function CallBackForm() {
    const [loading, setLoading] = useState(false); // ローディング状態の管理
    const router = useRouter(); // ルーターのインスタンス取得
    useEffect(() => {
        async function fetchData() {
            setLoading(true); // ローディング状態をtrueに設定
            // リダイレクト後のURLを取得
            try {
                const redirectUrl = window.location.href;
                if (!redirectUrl) {
                    throw new Error;
                } else {
                    // 特に処理がない
                }

                // 許可コードの抽出
                const code = extractCodeFromUrl(redirectUrl);
                if (!code) {
                    throw new Error;
                } else {
                    // 特に処理がない
                }

                // クライアントのタイムゾーンをサーバー側に送付する
                const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
                // 解析後に取得したユーザ情報による契約情報の検証
                const callback = await fetch("/api/callback", {
                    method: "POST", // POSTリクエスト
                    headers: {
                        "Content-Type": "application/json", // JSON形式のヘッダー
                        'Time-Zone': timezone,
                    },
                    body: JSON.stringify({
                        code: code,
                    }),
                });
                // API実行結果判定
                if (callback.ok) {
                    const callbackResult = await callback.json();
                    if (callbackResult.status === 200) {
                        setLoading(false); // ローディング状態をfalseに設定
                        router.refresh(); // ページを更新
                        router.push("/dashboard"); // ホームページにリダイレクト
                    } else {
                        router.push("/login?error07");
                    }
                } else {
                    throw new Error;
                }
            } catch (error) {
                // ログアウト処理
                await fetch("/api/logout", {
                    method: "POST", // POSTリクエスト
                    headers: {
                        "Content-Type": "application/json", // JSON形式のヘッダー
                    }
                });

                router.push("/login?error07");
            } finally {
                // ローディング状態をfalseに設定
                setLoading(false);
            }
        }
        fetchData();
    }, []);
    return (
        <div>
            <Spinner
                className={clsx("inline-block mr-2", { hidden: !loading })}
            /></div>
    );
}