/**
 * @file layout.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

// These styles apply to every route in the application
import "@/styles/globals.css";
import { Metadata } from "next"; // メタ情報インターフェース
import { FC, PropsWithChildren } from "react"; // 遅延読み込みコンポーネント

const title = "PC自動シャットダウンサービス";
const description = "PC自動シャットダウンサービス";

export const metadata: Metadata = {
  title,
  description,
  metadataBase: new URL("http://localhost:3000"),
};

const RootLayout: FC<PropsWithChildren> = function ({ children }) {
  return (
    <html lang="ja">
      <body className="font-body antialiased">{children}</body>
    </html>
  );
};

export default RootLayout;
