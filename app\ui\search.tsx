/**
 * @file search.tsx
 * @description 検索コンポーネントの実装
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { ChangeEvent, useEffect, useState } from "react";
import { HiOutlineSearch, HiX } from "react-icons/hi";
import { UserLogsSearchPattern } from "../lib/definitions";

// フィルターコンポーネント
export default function Search() {
  const searchParams = useSearchParams();
  const { replace } = useRouter();
  const pathname = usePathname();
  const [filter, setFilter] = useState("");

  useEffect(() => {
    setFilter(searchParams.get("filter")?.toString() || "");
  }, [searchParams]);

  const handleSearch = () => {
    const params = new URLSearchParams(searchParams);

    params.set("page", "1");

    if (filter) {
      params.set("filter", filter);
    } else {
      params.delete("filter");
    }
    replace(`${pathname}?${params.toString()}`);
  };

  const handleReset = () => {
    const params = new URLSearchParams(searchParams);

    setFilter("");
    params.set("page", "1");
    params.delete("filter");
    params.set("btnflg", "reset");
    replace(`${pathname}?${params.toString()}`);
  };

  // アルファベットと数字、アンダースコア以外の入力を制限する関数
  function restrictAlphanumeric(event: ChangeEvent<HTMLInputElement>): void {
    const value = event.target.value;
    const validValue = value.replace(UserLogsSearchPattern, "");

    // 無効な文字が含まれている場合、即座に修正してstateも更新
    if (value !== validValue) {
      event.target.value = validValue;
      setFilter(validValue);
    }
  }

  return (
    <div className="flex justify-center">
      <label htmlFor="table-search" className="sr-only">
        Search
      </label>
      <div className="relative mr-2 inline-block">
        <input
          type="text"
          id="small-input"
          className=" block w-full rounded-lg border border-gray-300 bg-gray-50 p-2 text-gray-900 focus:border-gray-300 focus:ring-gray-300 sm:text-xs"
          maxLength={50}
          placeholder="ユーザーID"
          value={filter}
          onInput={restrictAlphanumeric}
          onChange={(e) => {
            // onInputで既に処理済みの場合はそのまま使用、そうでなければフィルタリング
            const validValue = e.target.value.replace(UserLogsSearchPattern, "");
            setFilter(validValue);
          }}
        ></input>
      </div>
      <button
        type="button"
        className="toggle-full-view mr-2 flex h-9 w-9 items-center justify-center rounded-lg border border-gray-200 bg-white text-xs font-medium text-gray-700 hover:bg-gray-100 hover:text-blue-700 focus:ring-gray-300"
        onClick={() => {
          handleSearch();
        }}
      >
        <HiOutlineSearch className="text-lg text-gray-500" />
        <span className="sr-only">search</span>
      </button>
      <button
        type="button"
        className="toggle-full-view mr-2 flex h-9 w-9 items-center justify-center rounded-lg border border-gray-200 bg-white text-xs font-medium text-gray-700 hover:bg-gray-100 hover:text-blue-700 focus:ring-gray-300"
        onClick={() => {
          handleReset();
        }}
      >
        <HiX className="text-lg text-gray-500" />
        <span className="sr-only">clear</span>
      </button>
    </div>
  );
}
