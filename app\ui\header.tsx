/**
 * @file header.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { SessionData } from "@/app/lib/session";
import { FC, useEffect, useState } from "react";
import LogoutModal from "./logout-modal";
import MessageModal from "./message-modal";
import PasswordModal from "./password-modal";
import Spinner from "./spinner";
import { Modal } from "flowbite";
import { usePathname, useSearchParams } from "next/navigation";
import useSession from "../hooks/use-session";

interface HeaderProps {
  session: SessionData;
}
// ナビゲーションバーコンポーネント
const Header: FC<HeaderProps> = function ({ session }) {
  // const router = useRouter(); // ルーターのインスタンス取得
  const [message, setMessage] = useState(""); // メッセージ用の状態
  const [error, setError] = useState(""); // エラー用の状態
  const [passwordModal, setPasswordModal] = useState<Modal>(); // パスワードモーダルのインスタンス
  const [passwordModalVisible, setPasswordModalVisible] = useState(false); // パスワードモーダルの表示状態
  const [messageModal, setMessageModal] = useState<Modal>(); // メッセージモーダルのインスタンス
  const [logoutModal, setLogoutModal] = useState<Modal>(); // ログアウトモーダルのインスタンス
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { getSession } = useSession();

  // 初期化処理
  useEffect(() => {
    const initializeModal = async () => {
      const { initModals } = await import("flowbite");
      initModals();
      initPasswordModal();
      initMessageModal();
      initLogoutModal();
    };
    initializeModal();
  }, []);

  // パス名と検索パラメータが変更された場合、セッションを再取得
  useEffect(() => {
    getSession();
  }, [pathname, searchParams]);

  // メッセージまたはエラーが更新された場合の処理
  useEffect(() => {
    if (message || error) {
      messageModal!.show();
    } else {
      // 特に処理がない
    }
  }, [message, error, messageModal]);

  // パスワードモーダルを初期化
  const initPasswordModal = async () => {
    const $targetEl = document.getElementById("password-modal"); // モーダルのターゲット要素
    const options = {
      backdropClasses:
        "modal-backdrop bg-gray-900/50 dark:bg-gray-900/80 fixed inset-0 z-40", // 背景のスタイル
      closable: false, // モーダルを閉じるボタンの有効/無効
      onHide: () => {
        setPasswordModalVisible(false); // モーダル非表示時の処理
      },
      onShow: () => {
        setPasswordModalVisible(true); // モーダル表示時の処理
      },
    };
    const instanceOptions = {
      id: "password-modal", // モーダルのID
      override: true, // モーダルの設定を上書き
    };
    const { Modal } = await import("flowbite"); // Flowbiteライブラリを動的インポート
    const modal = new Modal($targetEl, options, instanceOptions); // モーダルインスタンスを作成
    setPasswordModal(modal); // 状態にセット
  };

  // メッセージモーダルを初期化
  const initMessageModal = async () => {
    const $targetEl = document.getElementById("message-modal");
    const options = {
      backdropClasses:
        "modal-backdrop bg-gray-900/50 dark:bg-gray-900/80 fixed inset-0 z-40",
      closable: false,
    };
    const instanceOptions = {
      id: "message-modal",
      override: true,
    };
    const { Modal } = await import("flowbite");
    const modal = new Modal($targetEl, options, instanceOptions);
    setMessageModal(modal);
  };

  // ログアウトモーダルを初期化
  const initLogoutModal = async () => {
    const $targetEl = document.getElementById("logout-modal");
    const options = {
      backdropClasses:
        "modal-backdrop bg-gray-900/50 dark:bg-gray-900/80 fixed inset-0 z-40",
      closable: false,
    };
    const instanceOptions = {
      id: "logout-modal",
      override: true,
    };
    const { Modal } = await import("flowbite");
    const modal = new Modal($targetEl, options, instanceOptions);
    setLogoutModal(modal);
  };

  // 各モーダルの操作関数
  const openPasswordModal = async () => {
    passwordModal!.show();
  };
  const closePasswordModal = () => {
    passwordModal!.hide();
  };
  const openMessageModal = (message: string) => {
    passwordModal!.hide();
    setMessage(message);
    setError("");
  };
  const closeMessageModal = () => {
    messageModal!.hide();
    setMessage("");
    setError("");
  };
  const openErrorModal = (error: string) => {
    passwordModal?.hide();
    logoutModal?.hide();
    setMessage("");
    setError(error);
  };
  const openLogoutModal = () => {
    logoutModal!.show();
  };
  const closeLogoutModal = () => {
    logoutModal!.hide();
  };

  return (
    <>
      <MessageModal
        message={message}
        error={error}
        onClose={closeMessageModal}
      />
      <LogoutModal onError={openErrorModal} onClose={closeLogoutModal} />
      <PasswordModal
        id={session ? session.user.id! : ""}
        userId={session ? session.user.userId! : ""}
        isOpen={passwordModalVisible}
        onClose={closePasswordModal}
        onOK={openMessageModal}
        onError={openErrorModal}
      />
      <header className="sticky top-0 z-20">
        <nav className="border-gray-700 bg-gray-800 pb-2 pt-4 sm:px-4">
          <div className="mb-2 mx-auto flex flex-wrap items-center justify-between">
            <a
              href="/"
              className="flex items-center space-x-3 rtl:space-x-reverse"
            >
              <span className="self-center whitespace-nowrap px-3 text-xl font-semibold text-white"></span>
            </a>
            <div className="z-10 flex md:order-2">
              <label className="mr-2 text-sm font-medium leading-8 text-white">
                ログインユーザー:
              </label>
              <label className="mr-5 text-sm font-medium leading-8 text-white">
                {(session && session.user.userId) || <Spinner />}
              </label>
              <button
                type="button"
                className="w-28 rounded bg-gradient-dark drop-shadow-dark shadow-dark px-3 py-2 text-center text-xs 
                font-medium text-transparent hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
                onClick={openLogoutModal}
                style={{ backgroundImage: "url(/icons/btn_logout.ico)" }}
              >
                ログアウト
              </button>
            </div>
          </div>
          <div className="mx-auto flex flex-wrap items-center justify-between">
            <div />
            <div className="z-10 flex md:order-2">
              <button
                type="button"
                className="w-28 rounded bg-gradient-dark drop-shadow-dark shadow-dark px-3 py-2 text-center text-xs 
                font-medium text-transparent hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
                onClick={openPasswordModal}
                style={{ backgroundImage: "url(/icons/btn_password.ico)" }}
              >
                パスワード変更
              </button>
            </div>
          </div>
        </nav>
      </header>
    </>
  );
};

export default Header;
