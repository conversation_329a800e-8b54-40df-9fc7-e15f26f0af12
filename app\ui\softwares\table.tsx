/**
 * @file table.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */
"use client";

import { PCSD_ERROR_MESSAGES } from "@/app/lib/definitions";
import { TableSkeleton } from "@/app/ui/skeletons";
import SoftwareOverviewModal from "@/app/ui/software-overview-modal";
import Tooltip from "@/app/ui/tooltip";
import Image from "next/image";
import { useEffect, useState } from "react";
import FeedbackModal from "../feedback-modal";
import Thead from "../thead";
import useSession from "@/app/hooks/use-session";

// ソフトウェアテーブルコンポーネント
export default function SoftwaresTable({
  page,
  size,
  sort,
  order,
}: {
  page: number;
  size: number;
  sort: string;
  order: "asc" | "desc";
}) {
  // ソフトウェアデータを管理する状態を定義
  const [softwares, setsoftwares] = useState<any[]>([]);
  // モーダルの状態を管理する
  const [softwareOveriewModal, setSoftwareOveriewModal] = useState<any | null>(
    null,
  );
  // 選択されたソフトウェアIDを保持
  const [selectedSoftwareId, setSelectedSoftwareId] = useState<string | null>(
    null,
  );
  // ローディング状態を管理
  const [loading, setLoading] = useState(false);
  // マスクの表示状態を管理
  const [isMaskVisible, setIsMaskVisible] = useState(false);
  const { getSession } = useSession();

  // コンポーネントの初期レンダリング時にデータをフェッチ
  useEffect(() => {
    // ソフトウェアデータを取得する
    const fetchSoftware = async () => {
      const formData = new FormData();
      formData.append("size", size.toString());
      formData.append("page", page.toString());
      formData.append("sort", sort);
      formData.append("order", order);

      const formObject = Object.fromEntries(formData);

      setLoading(true);

      // ソフトウェアデータをサーバーから取得
      const res = await fetch("/api/softwares", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formObject),
      });

      const data = await res.json();
      return data;
    };

    // データをフェッチして状態を更新
    const fetchData = async () => {
      // ソフトウェアデータをサーバーから取得
      const softwareData = await fetchSoftware();

      // apiエラー発生時の処理
      if (softwareData.error) {
        showErrorModal(softwareData.error);
        setsoftwares([]);
        setLoading(false);
        return;
      } else {
        // ソフトウェアデータをステートに保存
        setsoftwares(softwareData);
        setLoading(false);
      }
    };

    if (page > 0) {
      fetchData();
    }
  }, [order, page, size, sort]);

  // // モーダルを開くための関数
  const openModal = async (softwareId: string) => {
    getSession();
    setSelectedSoftwareId(softwareId); // 選択されたソフトウェアIDを保存

    // モーダル要素を取得
    const $targetEl = document.getElementById("software-overview-modal");
    const options = {
      backdropClasses:
        "modal-backdrop bg-gray-900/50 dark:bg-gray-900/80 fixed inset-0 z-40",
      closable: false,
    };
    const instanceOptions = {
      id: "software-overview-modal",
      override: true,
    };

    // Flowbiteのモーダルコンポーネントを動的にインポートして表示
    const { Modal } = await import("flowbite");
    const modal = new Modal($targetEl, options, instanceOptions);
    modal?.show();
    setSoftwareOveriewModal(modal);
  };

  // モーダルを閉じる関数
  const closeModal = () => {
    softwareOveriewModal?.hide();
    setSelectedSoftwareId("");
  };

  // ファイルダウンロードを処理する関数
  const handleDownloadClick = async (filePath: string) => {
    getSession();
    setIsMaskVisible(true);

    // ダウンロードリクエストのためのデータ作成
    const formData = new FormData();
    formData.append("filePath", filePath);
    const formObject = Object.fromEntries(formData);

    // ダウンロードリクエストを送信
    const response = await fetch("/api/softwares/download", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(formObject),
    });

    if (response.ok) {
      // ダウンロードするファイルのBlobデータを取得
      const blob = await response.blob();
      // ファイルをダウンロード
      downloadFile(blob, `${filePath}`);
    } else {
      // エラーメッセージを取得
      const data = await response.json();
      if (response.status === 500) {
        showErrorModal(
          PCSD_ERROR_MESSAGES.EMEC0014.replace("{0}", "ファイルのダウンロード"),
        );
        // マスクを非表示
        setIsMaskVisible(false);
      } else {
        // メッセージモーダルを表示
        showErrorModal(data.message);
        // マスクを非表示
        setIsMaskVisible(false);
      }
    }
  };

  // 指定のファイルをダウンロード
  const downloadFile = (blob: Blob, fileName: string) => {
    const normalizedPath = fileName.replace(/\\/g, "/");
    const parts = normalizedPath.split("/");
    const downloadFile = parts[parts.length - 1];

    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = downloadFile;
    document.body.appendChild(a);
    a.click();
    a.remove();
    setIsMaskVisible(false);
  };

  const [feedbackMsg, setFeedbackMsg] = useState<string | null>(null);
  const [feedbackError, setFeedbackError] = useState<string | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);

  // モーダルを開きメッセージを設定
  const showMessageModal = (newMessage: string) => {
    setFeedbackMsg(newMessage);
    setFeedbackError(null); // エラークリア
    setIsModalVisible(true); // モーダルを開く
  };
  // モーダルを開きエラーを設定
  const showErrorModal = (newError: string) => {
    setFeedbackError(newError);
    setFeedbackMsg(null); // メッセージクリア
    setIsModalVisible(true); // モーダルを開く
  };
  // モーダルを閉じる
  const closeFeedbackModal = () => {
    setIsModalVisible(false);
    setFeedbackError(null); // エラークリア
    setFeedbackMsg(null); // メッセージクリア
  };

  if (loading) {
    return <TableSkeleton />;
  } else {
    // 特に処理がない
  }

  return (
    <>
      {isModalVisible && (
        <FeedbackModal
          message={feedbackMsg}
          error={feedbackError}
          onClose={closeFeedbackModal}
        />
      )}
      {isMaskVisible && (
        <div className="fixed inset-0 bg-gray-900 opacity-50 z-40"></div>
      )}
      <SoftwareOverviewModal
        onClose={closeModal}
        selectedSoftwareId={selectedSoftwareId ?? ""}
      />
      <div className="overflow-y-auto">
        <table className="whitespace-nowrap w-full h-full text-left text-sm text-gray-500">
          <Thead
            headers={[
              { key: "no", label: "＃", css: "w-10", isNotSort: true },
              { key: "SOFTWARE_NAME", label: "ソフトウェア名" },
              { key: "VERSION_INFO", label: "バージョン情報" },
              { key: "overview", label: "概要", isNotSort: true },
              { key: "SOFTWARE_UPDATE_DATE", label: "更新日" },
              { key: "download", label: "ダウンロード", isNotSort: true },
            ]}
            defaultOrder="SOFTWARE_UPDATE_DATE"
            defaultSort="desc"
          />
          <tbody>
            {softwares?.length !== 0 ? (
              softwares?.map((software, index) => (
                <tr
                  key={software.ID}
                  className="border-b odd:bg-white even:bg-gray-50"
                >
                  <td className="px-6 py-3 text-center border-r">
                    {(page - 1) * size + index + 1}
                  </td>
                  <td className="max-w-96 px-6 py-3 border-r">
                    <div className="flex items-center gap-3 ">
                      <div className="flex items-center flex-shrink-0">
                        {software.NEW_MARK_ADD_FLAG === true ? (
                          <Image
                            src="/icons/new.ico"
                            className="w-8 h-8"
                            width={32}
                            height={32}
                            alt="new"
                          />
                        ) : (
                          <Image
                            src="/icons/transparent.png"
                            className="w-8 h-8 opacity-0"
                            width={32}
                            height={32}
                            alt=""
                          />
                        )}
                      </div>
                      <div className="flex-grow min-w-16">
                        <Tooltip
                          text={software.SOFTWARE_NAME}
                          torb={
                            index === size - 1 || index === softwares.length - 1
                              ? "top"
                              : "bottom"
                          }
                        >
                          <p className="truncate">{software.SOFTWARE_NAME}</p>
                        </Tooltip>
                      </div>
                    </div>
                  </td>
                  <td className="max-w-96 px-6 py-4 border-r">
                    <div className="flex items-center gap-3">
                      <div className="flex-grow min-w-16">
                        <Tooltip
                          text={software.VERSION_INFO}
                          torb={
                            index === size - 1 || index === softwares.length - 1
                              ? "top"
                              : "bottom"
                          }
                        >
                          <p className="truncate">{software.VERSION_INFO}</p>
                        </Tooltip>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 border-r">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center flex-shrink-0">
                        {software.SOFTWARE_OVERVIEW === null ||
                        software.SOFTWARE_OVERVIEW === "" ? (
                          <p></p>
                        ) : (
                          <a
                            href="#"
                            onClick={(e) => {
                              e.preventDefault();
                              openModal(software.ID);
                            }}
                          >
                            <Image
                              src="/icons/overview.ico"
                              className="cursor-pointer w-8 h-8"
                              width={32}
                              height={32}
                              alt="overview"
                            />
                          </a>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 border-r">
                    {software.SOFTWARE_UPDATE_YYYYMMDD}
                  </td>
                  <td className="min-w-48 px-6 py-4">
                    <div className="flex items-center gap-3 flex-shrink-0">
                      {software.SOFT_PACK_FILEPATH === null ||
                      software.SOFT_PACK_FILEPATH === "" ? (
                        <Image
                          src="/icons/transparent.png"
                          className="w-8 h-8 opacity-0"
                          width={32}
                          height={32}
                          alt=""
                        />
                      ) : (
                        <Tooltip
                          text={"クライアントソフトウェア一式ダウンロード"}
                          bigTip="big"
                          style={{ width: "10px", left: "50%" }}
                          torb={
                            index === size - 1 ||
                            index === softwares?.length - 1
                              ? "top"
                              : "bottom"
                          }
                        >
                          <Image
                            src="/icons/softPack.ico"
                            className="cursor-pointer w-8 h-8"
                            width={32}
                            height={32}
                            alt="softPack"
                            tabIndex={0}
                            onClick={() =>
                              handleDownloadClick(software.SOFT_PACK_FILEPATH)
                            }
                          />
                        </Tooltip>
                      )}
                      {software.SOFT_ONLY_FILEPATH === null ||
                      software.SOFT_ONLY_FILEPATH === "" ? (
                        <Image
                          src="/icons/transparent.png"
                          className="opacity-0"
                          width={32}
                          height={32}
                          alt=""
                        />
                      ) : (
                        <Tooltip
                          text={"クライアントソフトウェアダウンロード"}
                          bigTip="big"
                          style={{ width: "10px", left: "50%" }}
                          torb={
                            index === size - 1 ||
                            index === softwares?.length - 1
                              ? "top"
                              : "bottom"
                          }
                        >
                          <Image
                            src="/icons/softOnly.ico"
                            className="cursor-pointer w-8 h-8"
                            width={32}
                            height={32}
                            alt="softOnly"
                            tabIndex={0}
                            onClick={() =>
                              handleDownloadClick(software.SOFT_ONLY_FILEPATH)
                            }
                          />
                        </Tooltip>
                      )}
                      {software.DOCMENT_FILEPATH === null ||
                      software.DOCMENT_FILEPATH === "" ? (
                        <Image
                          src="/icons/transparent.png"
                          className="opacity-0"
                          width={32}
                          height={32}
                          alt=""
                        />
                      ) : (
                        <Tooltip
                          text={"各種ドキュメント一式ダウンロード"}
                          bigTip="big"
                          style={{ width: "10px", left: "50%" }}
                          torb={
                            index === size - 1 ||
                            index === softwares?.length - 1
                              ? "top"
                              : "bottom"
                          }
                        >
                          <Image
                            src="/icons/document.ico"
                            className="cursor-pointer w-8 h-8"
                            width={32}
                            height={32}
                            alt="document"
                            tabIndex={0}
                            onClick={() =>
                              handleDownloadClick(software.DOCMENT_FILEPATH)
                            }
                          />
                        </Tooltip>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td>
                  <div className="p-4"></div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </>
  );
}
