/**
 * @file thead.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

"use client";

import clsx from "clsx";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

interface TheadProps {
  defaultSort: string;
  defaultOrder: string;
}

// テーブルヘッダーコンポーネント
export default function CalendarThead({
  defaultOrder,
  defaultSort,
}: TheadProps) {
  const pathname = usePathname();
  const { replace } = useRouter();
  const searchParams = useSearchParams();
  const [sort, setSort] = useState("");
  const [order, setOrder] = useState("");

  useEffect(() => {
    setSort(searchParams.get("sort") || defaultOrder);
    setOrder(searchParams.get("order") || defaultSort);
  }, [searchParams]);

  const Icon = ({ isAsc }: { isAsc?: boolean }) => (
    <img
      src="/screenopenicon16.png"
      className={clsx("icon h-4 w-4 transform-gpu", { "rotate-180": isAsc })}
      alt="sort"
    />
  );

  function toogleOrder() {
    const newOrder = order === "asc" ? "desc" : "asc";
    setOrder(newOrder);

    return newOrder;
  }

  function handleSort(sortKey: string) {
    const params = new URLSearchParams(searchParams);
    if (sortKey === sort) {
      setSort(sortKey);
      params.set("order", toogleOrder());
    } else {
      setSort(sortKey);
      setOrder("asc");
      params.set("sort", sortKey);
      params.set("order", "asc");
    }

    params.set("page", "1");
    replace(`${pathname}?${params.toString()}`);
  }

  return (
  <thead className="bg-gray-50 text-xs text-gray-700 border">
    <tr>
      <th key="no" scope="col" className="px-6 py-3 row-span-2 w-[60px]" rowSpan={2}>
        <div className="flex justify-between hover:opacity-80 transition duration-300">
          <span>＃</span>
        </div>
      </th>
      <th key="GROUP_NAME" scope="col" className="px-6 py-3 border-l" rowSpan={2}>
        <div className="flex justify-between cursor-pointer hover:opacity-80 transition duration-300" onClick={() => handleSort("GROUP_NAME")}>
          <span>カレンダーグループ名</span>
          {"GROUP_NAME" === sort ? (<Icon isAsc={order === "asc"} /> ) : (<div className="w-4"></div>)}
        </div>
      </th>
      <th key="caldenartool" scope="col" className="px-6 py-3 border-l border-b" colSpan={2}>
        <div className="flex justify-between hover:opacity-80 transition duration-300" >
          <span>カレンダー作成ツール</span>
        </div>
      </th>
      <th key="clientsetting" scope="col" className="px-6 py-3 border-l border-b" colSpan={2}>
        <div className="flex justify-between hover:opacity-80 transition duration-300" >
          <span>クライアント動作設定</span>
        </div>
      </th>
    </tr>
    <tr>
      <th key="caldenartoolname" scope="col" className="px-6 py-3 border-l">
        <div className="flex justify-between cursor-pointer hover:opacity-80 transition duration-300" onClick={() => handleSort("CALENDAR_TOOL_NAME")}>
          <span>ファイル名</span>
          {"CALENDAR_TOOL_NAME" === sort ? (<Icon isAsc={order === "asc"} /> ) : (<div className="w-4"></div>)}
        </div>
      </th>
      <th key="uploaddate" scope="col" className="px-6 py-3 border-l" style={{ width: '85px' }}>
        <div className="flex justify-between cursor-pointer hover:opacity-80 transition duration-300" onClick={() => handleSort("UPLOAD_DATE")}>
          <span>アップロード日時</span>
          {"UPLOAD_DATE" === sort ? (<Icon isAsc={order === "asc"} /> ) : (<div className="w-4"></div>)}
        </div>
      </th>
      <th key="setting" scope="col" className="px-6 py-3 border-l">
        {/* <div className="flex justify-between cursor-pointer hover:opacity-80 transition duration-300" onClick={(e) => handleSort("INI_DAT")}> */}
        <div className="flex justify-between cursor-pointer hover:opacity-80 transition duration-300" onClick={() => handleSort("INI_DAT")}>
          <span>設定</span>
          {"INI_DAT" === sort ? (<Icon isAsc={order === "asc"} /> ) : (<div className="w-4"></div>)}
        </div>
      </th>
      <th key="clientconfigupdate" scope="col" className="px-6 py-3 border-l"  style={{ width: '85px' }}>
        <div className="flex justify-between cursor-pointer hover:opacity-80 transition duration-300" onClick={() => handleSort("CLIENT_CONFIG_UPDATE")}>
          <span>更新日時</span>
          {"CLIENT_CONFIG_UPDATE" === sort ? (<Icon isAsc={order === "asc"} /> ) : (<div className="w-4"></div>)}
        </div>
      </th>
    </tr>
  </thead>
  );
}
