# Prisma docs: https://www.prisma.io/docs/concepts/database-connectors/sql-server
PGSQL_HOST=pcsd-database.postgres.database.azure.com
PGSQL_PORT=5432
PGSQL_DATABASE_WORKFLOW=workflowDB_main

PGSQL_PRISMA_DEFAULT_URL=postgresql://authuser:<EMAIL>:5432/postgres

# Seconds - How long until an idle session expires and is no longer valid.
JWT_MAX_AGE_SECONDS=1800
# Logging Levels: https://github.com/winstonjs/winston#logging-levels
LOG_LEVEL=info
# The number of seconds after which the cache should be revalidated.
APP_CACHE_TTL_SECONDS=7200

PCSD_FILE_PATH=F:\PCSD_FILE
USER_LOG_FILE_PATH=customer
SOFTWAREA_FILE_PATH=media
USER_WIN_LOG_STR=userwinlog
USER_LOG_FILE_NAME=userwinlog.csv

# KEYCLOAK
KEYCLOAK_PUBLIC_DOMAIN_NAME=http://**************:8080
KEYCLOAK_INTERNAL_DOMAIN_NAME=http://**************:8080
KEYCLOAK_REALM=PCSDRealm
KEYCLOAK_CLIENT=PCSDClient
KEYCLOAK_REDIRECT_URL=http://**************:3000/callback
KEYCLOAK_CLIENT_SECRET=UQ1tb3PLC9tsURkgzTBXUuTgkPRcHdOq

AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=jp1blob;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net

# Next.js Public Variables
# NEXT_PUBLIC_WORKFLOW_CONSTANT_LINK: Public URL for workflow
NEXT_PUBLIC_WORKFLOW_CONSTANT_LINK=pcsd-service.run-stg.rel.d3-platform.hitachi-solutions.com/workflow