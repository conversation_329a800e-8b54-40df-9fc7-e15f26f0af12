/**
 * @file tasks.ts
 * @description タスク一覧機能のデータアクセス層
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

"use server";

import prisma from "@/app/lib/prisma";
import { LogFunctionSignature } from "@/app/lib/logger";
import { handleServerError } from "@/app/lib/portal-error";
import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";
import { formatDate } from "@/app/lib/utils";

interface TaskRecord {
  TASK_ID: string;
  REQUESTING_USER_ID: string;
  TASK_NAME: string;
  STATUS: string;
  STARTED_AT?: Date | null;
  COMPLETED_AT?: Date | null;
  ERROR_MESSAGE?: string | null;
}

interface FormattedTaskRecord extends TaskRecord {
  STARTED_AT_YYYYMMDDHHMMSS: string;
  COMPLETED_AT_YYYYMMDDHHMMSS: string;
}

/**
 * 大文字ソート条件を変換し、SQLのORDER BY句用の文字列を生成する。
 *
 * この関数は、指定されたソートフィールドとソート順序をもとに、
 * SQLのORDER BY句に使用できる文字列を生成します。
 * 特に、指定されたフィールドが存在するかどうかを基準にソートを実現します。
 * また、ソートフィールドが第2ソートキーと異なる場合は、
 * 指定された第2ソートキーを追加します。
 *
 * @param sort - ソートするフィールド名
 * @param order - ソート順序（'asc'または'desc'）
 * @param secondSortKeyUper - 第2ソートキー（例: 'SOFTWARE_NAME'）。ソート順序は'asc'で固定します。
 * @returns SQLのORDER BY句用の文字列
 *
 * @remarks
 * - ソートフィールド名と第2ソートキーは、内部的に大文字に変換されます。
 * - 生成されたORDER BY句は、大文字のフィールド名を使用します。
 */
function formatSortConditionsUperCase(
  sort: string,
  order: string,
  secondSortKeyUper: string,
): string {
  const sortUperStr = sort.toUpperCase();
  const orderLowerStr = order.toLowerCase();

  let orderByStr = "";

  if (
    sortUperStr === "LOG_TIME" ||
    sortUperStr === "STARTED_AT" ||
    sortUperStr === "COMPLETED_AT"
  ) {
    if (orderLowerStr === "desc") {
      orderByStr = `"${sortUperStr}" desc NULLS LAST`;
    } else {
      orderByStr = `"${sortUperStr}" asc NULLS FIRST`;
    }
  } else {
    orderByStr = `"${sortUperStr}" ${orderLowerStr}`;
  }

  // ソートフィールドが第2ソートキーでない場合は、指定された第2ソートキーを追加する
  if (sortUperStr !== secondSortKeyUper) {
    orderByStr += `, "${secondSortKeyUper}" asc`;
  }

  return orderByStr;
}

/**
 * タスク一覧機能のデータアクセスクラス
 */
class TasksData {
  /**
   * 指定された1ページあたりの件数に基づき、`TASKS`テーブルの総ページ数を計算して取得します。
   * 主にフロントエンドのページネーションコンポーネントの構築に使用されます。
   * @param {number} size - 1ページに表示するタスクの件数。
   * @returns {Promise<number | undefined>} - 計算された総ページ数を返します。エラー発生時はundefinedを返します。
   */
  @LogFunctionSignature()
  static async fetchTasksListPages(size: number): Promise<number | undefined> {
    try {
      const session = await getIronSession<SessionData>(
        cookies(),
        sessionOptions,
      );

      const taskCnt = await prisma.$queryRawUnsafe<{ cnt: number }[]>(
        `
        SELECT COUNT(*) AS cnt
        FROM
            "TASKS"
        WHERE "SCHEMA_NAME" = $1;
        `,
        session.user.schemaName,
      );

      // クエリ結果から総件数を取得します。結果が存在しない場合は0をデフォルト値とします。
      const count = Number(taskCnt[0]?.cnt || 0);
      // 総件数を1ページあたりの件数で割り、小数点以下を切り上げて総ページ数を算出します。
      return Math.ceil(count / size);
    } catch (error) {
      // サーバーまたはデータベースで発生したエラーを処理します。
      handleServerError(error);
      return undefined;
    }
  }

  /**
   * ページネーションおよびソート条件に基づき、`TASKS`テーブルからタスクのリストを取得し、日付フィールドをフォーマットします。
   * @param {string} schemaName - スキーマ名
   * @param {number} size - 1ページに表示するタスクの件数。
   * @param {number} page - 取得対象の現在のページ番号（1から始まります）。
   * @param {string} sort - ソート対象のカラム名。
   * @param {string} order - ソート順（'asc'または'desc'）。
   * @returns {Promise<FormattedTaskRecord[] | undefined>} - 日付がフォーマットされたタスクのリスト配列を返します。エラー発生時はundefinedを返します。
   */
  @LogFunctionSignature()
  static async fetchTasksLists(
    schemaName: string,
    size: number,
    page: number,
    sort: string,
    order: string,
  ): Promise<FormattedTaskRecord[] | undefined> {
    try {
      // 現在のユーザーセッション情報を取得します（主にタイムゾーンの取得が目的です）。
      const session = await getIronSession<SessionData>(
        cookies(),
        sessionOptions,
      );

      // 渡されたソートパラメータを基に、SQLのORDER BY句を動的に生成します。
      const orderByStr = formatSortConditionsUperCase(sort, order, "TASK_NAME");
      const offset = (page - 1) * size;
      // ページネーションとソート条件を指定して、タスクデータを取得するネイティブSQLクエリを実行します。
      // パフォーマンス向上のため、画面表示に必要な列のみを取得します。
      const tasklists = await prisma.$queryRawUnsafe<TaskRecord[]>(
        `
        SELECT
            "TASK_ID",
            "REQUESTING_USER_ID",
            "TASK_NAME",
            "STATUS",
            "STARTED_AT",
            "COMPLETED_AT",
            "ERROR_MESSAGE"
        FROM
            "TASKS"
        WHERE "SCHEMA_NAME" = $1
        ORDER BY ${orderByStr}
        LIMIT $2
        OFFSET $3
        ;
        `,
        schemaName,
        size,
        offset,
      );

      // クエリ結果をループ処理し、タイムスタンプのフィールドをフォーマットします。
      // セッションから取得したユーザーのタイムゾーン(tz)を使い、UTC時刻をユーザーのローカル時刻に変換します。
      const cachedTaskLists = tasklists.map((task) => {
        return {
          ...task,
          STARTED_AT_YYYYMMDDHHMMSS: task.STARTED_AT
            ? formatDate(task.STARTED_AT, session.user.tz)
            : "",
          COMPLETED_AT_YYYYMMDDHHMMSS: task.COMPLETED_AT
            ? formatDate(task.COMPLETED_AT, session.user.tz)
            : "",
        };
      });

      return cachedTaskLists;
    } catch (error) {
      handleServerError(error);
      return undefined;
    }
  }

  /**
   * タスクIDに基づき、特定のタスクがダウンロード可能であるかを検証します。
   * 検証には、タスクの存在確認、ステータスが'COMPLETED'であること、およびZIPファイルのパスが有効であることの確認が含まれます。
   * @param {string} taskId - 取得対象タスクの一意な識別子（UUID形式の文字列）。
   * @returns {Promise<{ ZIP_BLOB_PATH: string } | null | undefined>} - タスクが有効でダウンロード可能な場合、Blobパスを含むオブジェクトを返します。それ以外の場合はnullを返します。DBエラー時はundefinedを返す可能性があります。
   */
  @LogFunctionSignature()
  static async fetchTaskForDownload(taskId: string): Promise<{ ZIP_BLOB_PATH: string } | null | undefined> {
    try {
      // ネイティブSQLを実行し、タスクIDを基にZIPファイルのBlobパスとタスクのステータスを取得します。
      // 注意: ここで渡されたtaskId文字列を`::uuid`型に明示的にキャストし、データベースのフィールド型と一致させています。
      const tasks = await prisma.$queryRawUnsafe<
        { ZIP_BLOB_PATH: string | null; STATUS: string }[]
      >(
        `
      SELECT "ZIP_BLOB_PATH", "STATUS"
      FROM "TASKS"
      WHERE "TASK_ID" = $1::uuid
      `,
        taskId,
      );

      // クエリ結果が空の場合、指定されたタスクIDが存在しないことを意味します。
      if (tasks.length === 0) {
        return null;
      }

      const task = tasks[0];

      // タスクのステータスが'COMPLETED'でない場合、ダウンロード不可と判定します。
      if (task.STATUS !== "COMPLETED") {
        return null;
      }

      // ZIPファイルのパスが存在しない、または空文字列の場合、ダウンロード不可と判定します。
      if (!task.ZIP_BLOB_PATH || task.ZIP_BLOB_PATH.trim() === "") {
        return null;
      }

      // 全ての検証を通過した場合、ZIPファイルのパス情報を返します。
      return { ZIP_BLOB_PATH: task.ZIP_BLOB_PATH };
    } catch (error) {
      // データベースエラーなどの予期せぬエラーが発生した場合、エラーハンドラで処理します。
      handleServerError(error);
      return undefined;
    }
  }

  /**
   * 現在「圧縮中(COMPRESSING)」状態のタスク（TASKS）の数を取得します。
   * @returns 圧縮中のタスク数
   */
  @LogFunctionSignature()
  static async fetchtaskCompressingCnt() {
    try {
      const taskCompressingCnt = await prisma.$queryRawUnsafe<
        { cnt: number }[]
      >(
        `
        SELECT COUNT(*) AS cnt
        FROM "TASKS"
        WHERE "STATUS"= $1;
        `,
        "COMPRESSING",
      );

      return Number(taskCompressingCnt[0]?.cnt || 0);
    } catch (error) {
      handleServerError(error);
      throw error;
    }
  }
}

// "use server" 文件では async 関数のみエクスポート可能なため、
// TasksData クラスの静的メソッドを個別にエクスポート
export const fetchTasksListPages = TasksData.fetchTasksListPages;
export const fetchTasksLists = TasksData.fetchTasksLists;
export const fetchTaskForDownload = TasksData.fetchTaskForDownload;
export const fetchtaskCompressingCnt = TasksData.fetchtaskCompressingCnt;
