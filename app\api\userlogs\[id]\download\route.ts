/**
 * @file route.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { NextRequest, NextResponse } from "next/server";
import { ENV } from "@/app/lib/definitions";
import archiver from "archiver";
import { handleApiError } from "@/app/lib/portal-error";
import { getContainerClient } from "@/app/lib/azureStorage";
import { Readable } from "stream";
import { convertToZipUTC } from "@/app/lib/utils";
import { fetchUserlogById } from "@/app/lib/data/userlogs";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    // パラメータからユーザーIDを取得
    const userid = params.id;
    const SCHEMA_NAME = await fetchUserlogById(userid);

    // Azure Blob Storage からコンテナクライアントを取得する
    const containerClient = getContainerClient(
      ENV.AZURE_STORAGE_LOG_CONTAINER_NAME,
    );
    // ユーザーIDを基にフォルダパスを作成
    const folderPath = [
      SCHEMA_NAME,
      ENV.AZURE_STORAGE_LOG_BASE_PATH,
      userid,
    ].join("/");
    const prefix = `${folderPath}/`;
    console.log(`フォルダパス: ${folderPath}`);
    // フォルダ内のすべてのファイルとディレクトリを取得
    const listBlobsResponse = containerClient.listBlobsFlat({ prefix });

    // ダウンロード可能なファイルがあるかどうか
    let isFileExisted = false;

    // ファイルとディレクトリ情報を格納する配列
    const blobs: { name: string; properties: any }[] = [];

    // フォルダ内のすべてのファイルとディレクトリを取得する
    for await (const item of listBlobsResponse) {
      blobs.push(item);

      if (item.name.startsWith(prefix)) {
        isFileExisted = true;
      }
    }

    // フォルダ内にファイルが存在しない場合
    if (!isFileExisted) {
      return NextResponse.json(
        { error: "ダウンロードできる対象は存在しません。" },
        { status: 404 },
      );
    }

    // Node.jsのストリームをWeb Streams APIに変換するためのTransformStreamを作成
    const { readable, writable } = new TransformStream();

    // archiverを使ってZIP圧縮用のストリームを作成
    const archive = archiver("zip", {
      zlib: { level: 6 },
    });

    // Node.jsストリーム（archive）からデータを読み取り、Web Streams APIのwritableに書き込む
    const writer = writable.getWriter();

    // 'data'イベントで圧縮データを受け取り、Web Streams APIのwritableに書き込み
    archive.on("data", (chunk: Buffer) => {
      writer.write(chunk); // チャンクをWeb Streams APIに書き込み
    });

    // 'end'イベントで圧縮完了時にストリームを閉じる
    archive.on("end", () => {
      writer.close(); // ストリームを終了
    });

    // 'error'イベントでエラーが発生した場合にストリームを中断
    archive.on("error", (err: Error) => {
      writer.abort(err); // エラー発生時にストリームを中断
    });

    // フォルダ内のすべてのブロブを圧縮する
    for await (const item of blobs) {
      const blobClient = containerClient.getBlobClient(item.name);
      const downloadBlockBlobResponse = await blobClient.download(0);

      // ブロブの内容をストリームとして取得
      const blobStream =
        downloadBlockBlobResponse.readableStreamBody as Readable;

      if (blobStream) {
        // folderPath を削除して、userid からの相対パスを使用
        const fileName = item.name.replace(prefix, "");
        // ファイルの最終更新日時を設定
        // ローカル時間に変換
        const localLastModified = convertToZipUTC(item.properties.lastModified);
        // ブロブの内容をarchiverに追加
        archive.append(blobStream, {
          name: fileName,
          date: localLastModified,
        });
      }
    }

    // 圧縮を開始するためにfinalizeを呼び出す
    await archive.finalize();

    // Web Streams APIのreadableをNext.jsのレスポンスとして返却
    return new NextResponse(readable, {
      headers: {
        "Content-Type": "application/zip", // レスポンスのMIMEタイプをZIPに設定
        "Content-Disposition": 'attachment; filename="archive.zip"', // ダウンロードするファイル名を設定
        "Transfer-Encoding": "chunked",
        "Cache-Control": "no-cache",
      },
    });
  } catch (error) {
    return handleApiError(error);
  }
}
