.tooltip {
  position: relative;
}

.tooltip .tooltipTextTop {
  bottom: 100%;
}

.tooltip .tooltipTextBottom {
  top: 100%;
}

.tooltip .tooltipText {
  visibility: hidden;
  opacity: 0;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  color: #fff;
  padding: 5px;
  border-radius: 4px;
  max-width: 300px;
  min-width: fit-content;
  line-height: 1.2;
  width: 110px;
  text-align: left;
  transition: opacity 0.2s;
  z-index: 9999;

  white-space: nowrap;
  text-overflow: ellipsis;
}

.tooltip .tooltipTextBig {
  visibility: hidden;
  opacity: 0;
  position: absolute;
  left: 800%;
  transform: translateX(-50%);
  background-color: #333;
  color: #fff;
  padding: 5px;
  border-radius: 4px;
  min-width: fit-content;
  line-height: 1.2;
  width: 450px;
  text-align: left;
  transition: opacity 0.2s;
  z-index: 9999;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.tooltip:hover .tooltipText, 
.tooltip:hover .tooltipTextBig {
  visibility: visible;
  opacity: 1;
}