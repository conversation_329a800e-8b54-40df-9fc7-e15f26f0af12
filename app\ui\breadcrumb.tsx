/**
 * @file breadcrumb.tsx
 * @description 
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { navLinks, otherLinks } from "../lib/definitions";

const findNavLinks = (
  targetPath: string,
): { firstLevel: string; secondLevel: string } | null => {
  for (const link of navLinks) {
    const matchingSubLink = link.subs.find((sub) => sub.href === targetPath);

    if (matchingSubLink) {
      return { firstLevel: link.name, secondLevel: matchingSubLink.breadName ?? matchingSubLink.name };
    } else {
      // 特に処理がない
    }
  }

  for (const link of otherLinks) {
    const matchingSubLink = link.subs.find((sub) => {
      const pattern = new RegExp(sub.href.replace("[id]", "([^/]+)").replace("[groupname]", "([^/]+)"));
      return pattern.test(targetPath);
    });

    if (matchingSubLink) {
      return { firstLevel: link.name, secondLevel: matchingSubLink.name };
    } else {
      // 特に処理がない
    }
  }

  return null;
};

// パンくずリストコンポーネント
export default function Breadcrumb() {
  const pathname = usePathname();
  const [nav, setNav] = useState<{
    firstLevel: string;
    secondLevel: string;
  } | null>(null);

  useEffect(() => {
    setNav(findNavLinks(pathname));
  }, [pathname]);

  return (
    <nav
      className="shrink-0 h-10 border-b border-b-[#424242] bg-gray-600 bg-gradient-header px-5 text-sm font-medium text-gray-300 shadow-inner drop-shadow"
      aria-label="Breadcrumb"
    >
      <div className="inline-flex h-full w-full items-center justify-between">
        <ol className="inline-flex items-center">
          <li aria-current="page">
            <div className="text-[16px] flex items-center">
              <span className="ms-1 text-sm text-[16px] font-medium text-gray-300 md:ms-2">
                {nav && nav.secondLevel}
              </span>
            </div>
          </li>
        </ol>
      </div>
    </nav>
  );
}
