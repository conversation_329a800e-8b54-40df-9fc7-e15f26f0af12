import styles from '@/styles/tooltip.module.css';
import { sanitize } from "isomorphic-dompurify";

const Tooltip = ({
  text,
  children,
  torb,
  bigTip,
  style,
  visible = true, 
}: {
  text: string,
  children: any,
  torb?: string,
  bigTip?: string,
  style?: React.CSSProperties,
  visible?: boolean,
}) => {

  const tooltipTopOrBottom = torb === 'top' ? styles.tooltipTextTop : torb === 'bottom' ? styles.tooltipTextBottom : '';
  const tooltipClass = bigTip === 'big' ? styles.tooltipTextBig : styles.tooltipText;
  return (
    <div className={styles.tooltip}>
      {children}
      {visible && text && (
      <span
        className={`${tooltipClass} ${tooltipTopOrBottom}`}
        dangerouslySetInnerHTML={{ __html: sanitize(text) }}
        style={style}
      ></span>
      )}
    </div>
  );
};

export default Tooltip;