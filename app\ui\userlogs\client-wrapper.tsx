/**
 * @file client-wrapper.tsx
 * @description 利用者環境ログページのクライアントサイドのロジックとレイアウトをすべて担当するコンポーネント。
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */
"use client";

import { useState } from "react";
import Table from "@/app/ui/userlogs/table";
import DownloadButton from "@/app/ui/download-button";

export default function UserlogsClientWrapper({
  blobInfos,
  allUserIdsForDownload,
  filter,
  page,
  size,
  search,
  pagination,
}: {
  blobInfos: Array<[string, string[]]>;
  allUserIdsForDownload: string[];
  filter: string;
  page: number;
  size: number;
  search: React.ReactNode;
  pagination: React.ReactNode;
}) {
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);

  const handleSelectBlob = (userId: string, isSelected: boolean) => {
    setSelectedUserIds((prev) =>
      isSelected ? [...prev, userId] : prev.filter((id) => id !== userId),
    );
  };

  const handleClearBlob = () => {
    setSelectedUserIds([]);
  };

  const handleSelectAllBlob = (allUserIdsOnPage: string[]) => {
    setSelectedUserIds(allUserIdsOnPage);
  };

  return (
    // <></> を使い、不要なdivを削除
    <>
      {/* 以前のレイアウト構造をここに再現する */}
      <div className="text-base">
        <div className="flex items-center justify-between">
          {/* 1. サーバーから渡されたSearchコンポーネントを配置 */}
          {search}

          {/* 2. ダウンロードボタンを同じ行に配置 */}
          <div className="ml-auto">
            <DownloadButton
              url="/api/userlogs/download"
              zipFileName="チェック一括"
              buttonText="チェック一括ダウンロード"
              params={selectedUserIds}
            />
            <DownloadButton
              url="/api/userlogs/download"
              zipFileName="全件一括"
              buttonText="全件一括ダウンロード　　"
              allUserids={allUserIdsForDownload}
            />
          </div>
        </div>
      </div>

      <div className="text-base">
        <div className="flex items-center justify-between">
          <div className="flex-column flex flex-wrap items-center justify-between rounded-t-xl">
            {/* 3. サーバーから渡されたPaginationコンポーネントを配置 */}
            {pagination}
          </div>
        </div>
      </div>

      {/* 4. テーブルを配置 */}
      <div className="relative overflow-x-hidden shadow-md rounded-t-lg overflow-y-auto">
        <Table
          blobInfos={blobInfos}
          hasBlobInfos={blobInfos.length > 0}
          onSelectBlob={handleSelectBlob}
          onClearBlob={handleClearBlob}
          onSelectAllBlob={handleSelectAllBlob}
          filter={filter}
          page={page}
          size={size}
        />
      </div>
    </>
  );
}
