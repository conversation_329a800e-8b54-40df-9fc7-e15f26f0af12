/**
 * @file info-modal.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

"use client";

import { ModalProps } from "../lib/definitions";
import React, { useEffect } from "react";

// メッセージダイアログコンポーネント
interface InfoModalProps extends ModalProps {
  message?: string | null;
  error?: string | null;
}

export default function InfoModal({ message, error, onClose }: InfoModalProps) {
  const closeModal = () => {
    const backdrop = document.querySelector(".modal-backdrop");
    if (backdrop) {
      backdrop.remove();
    } else {
      // 特に処理がない
    }
    onClose && onClose();
  };

  useEffect(() => {
    const initModal = async () => {
      const $targetEl = document.getElementById("info-modal");
      const options = {
        backdropClasses:
          "modal-backdrop bg-gray-900/50 dark:bg-gray-900/80 fixed inset-0 z-40",
        closable: false,
      };
      const instanceOptions = {
        id: "info-modal",
        override: true,
      };
      const { Modal } = await import("flowbite");
      const modal = new Modal($targetEl, options, instanceOptions);

      if (message || error) {
        modal.show();
      } else {
        modal.hide();
      }

      return () => {
        modal.hide();
      };
    };

    initModal();
  }, [message, error]);

  return (
    <div
      id="info-modal"
      tabIndex={-1}
      className="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full"
    >
      <div className="relative w-full max-w-lg max-h-full">
        <div className="relative rounded shadow bg-gray-600">
          <div className="flex items-center justify-between p-4 border-b rounded-t bg-gradient-header">
            <h3 className="text-lg font-semibold text-white">
              {error ? "エラー" : "メッセージ"}
            </h3>
            <button
              type="button"
              className="text-gray-400 bg-transparent rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center hover:bg-gray-600 hover:text-white"
              onClick={closeModal}
            >
              <svg
                className="w-3 h-3"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 14 14"
              >
                <path
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                />
              </svg>
              <span className="sr-only">Close modal</span>
            </button>
          </div>
          <div className="h-35 px-8 py-4 space-y-4 bg-white text-base font-medium">
            <div className="flex font-normal">
              <img
                src={error ? "/dialogerror_32.png" : "/dialoginfo_32.png"}
                className="w-8 h-8 me-2 inline-block"
                alt="info"
              />
              <span id="messid" className="whitespace-pre-line">
                {message || error}
              </span>
            </div>
          </div>
          <div className="flex flex-row-reverse items-center p-4 border-t rounded-b bg-gradient-header">
            <button
              onClick={closeModal}
              type="button"
              className="w-28 rounded bg-gradient-dark px-3 py-2 text-center text-xs font-medium text-transparent 
              drop-shadow-dark shadow-dark hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
              style={{ backgroundImage: "url(/icons/btn_cancel.ico)" }}
            >
              キャンセル
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
