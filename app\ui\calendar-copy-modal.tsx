/**
 * @file calendar-copy-modal.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { PCSD_ERROR_MESSAGES, PCSD_INFO_MESSAGES } from "@/app/lib/definitions";
import ConfirmModal from "@/app/ui/confirm-modal";
import FeedbackModal from "@/app/ui/feedback-modal";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import Tooltip from "@/app/ui/tooltip";
import useSession from "@/app/hooks/use-session";
import { sanitize } from "isomorphic-dompurify";
import { formatMessage } from "../lib/utils";

// Modal コンポーネントのプロパティ型定義
type ModalProps = {
  onClose: (isRefresh: boolean) => void; // モーダルを閉じるためのコールバック関数
  selectedCalendarId: string; // 選択されたカレンダーのID
  selectedCalendarName: string; // 選択されたカレンダーの名前
  isOpen: boolean; // モーダルの表示状態
};

const Modal = ({
  onClose,
  selectedCalendarId,
  selectedCalendarName,
  isOpen,
}: ModalProps) => {
  const router = useRouter(); // ルーターのフックを使用して現在のパスと検索パラメータを取得
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams);
  const [allCalendars, setAllCalendars] = useState<any[]>([]); // すべてのカレンダーのリスト
  const [selectedCalendars, setSelectedCalendars] = useState<Set<string>>(
    new Set(),
  ); // 選択されたカレンダーのセット
  const [selectAll, setSelectAll] = useState(false); // 全てのカレンダーを選択するかどうかのフラグ
  const [navigation, setNavigation] = useState<boolean>(false); // ページ遷移の制御用フラグ
  const { getSession } = useSession();

  let rowNum = 0; // 行の数を追跡する変数
  useEffect(() => {
    const fetchAllCalendars = async () => {
      const responseAllCals = await fetch("/api/calendars?isAll=1"); // APIからすべてのカレンダー情報を取得
      const data = await responseAllCals.json();
      if (data.error) {
        setAllCalendars([]);
      } else {
        setAllCalendars(data); // 取得したデータをステートに設定
      }
    };

    // データ存在しない場合にカレンダー情報をフェッチ
    if (allCalendars.length === 0) {
      fetchAllCalendars(); // カレンダー情報のフェッチを実行
    }
  }, [isOpen]);

  const closeModal = () => {
    const pageNum = params.get("page");
    const isFailed = !!feedbackError;

    setIsModalVisible(false); // モーダルを非表示にする
    setFeedbackError(null); // エラーメッセージをクリア
    setFeedbackMsg(null); // 成功メッセージをクリア

    if (!isFailed) {
      setSelectedCalendars(new Set()); // 選択状態をリセット
      setSelectAll(false); // 全て選択のフラグをリセット
      onClose(pageNum === "1"); // 外部の閉じるコールバックを呼び出す
      setNavigation(true); // ナビゲーションフラグを設定
    }
  };

  const closeModalWithoutNav = () => {
    setSelectedCalendars(new Set()); // 選択状態をリセット
    setSelectAll(false); // 全て選択のフラグをリセット

    setIsModalVisible(false); // モーダルを非表示にする
    setFeedbackError(null); // エラーメッセージをクリア
    setFeedbackMsg(null); // 成功メッセージをクリア

    onClose(false); // 外部の閉じるコールバックを呼び出す
  };

  useEffect(() => {
    if (navigation) {
      // ナビゲーションフラグがtrueの場合
      params.set("page", "1");
      router.replace(`${pathname}?${params.toString()}`);
    }
  }, [navigation, searchParams]);

  const handleCheckboxChange = (id: string) => {
    setSelectedCalendars((prev) => {
      const updated = new Set(prev);
      if (updated.has(id)) {
        updated.delete(id);
      } else {
        updated.add(id);
      }
      return updated;
    });
  };

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedCalendars(new Set());
    } else {
      const allIds = new Set(
        allCalendars
          .map((calendar) => calendar.ID)
          .filter((id) => id !== selectedCalendarId),
      );
      setSelectedCalendars(allIds);
    }
    setSelectAll(!selectAll);
  };

  const copyIniDat = async () => {
    setConfirmMsg(`${PCSD_INFO_MESSAGES.IMEC0001}`);
    setIsConfirmModalVisible(true);
  };

  const [feedbackMsg, setFeedbackMsg] = useState<string | null>(null);
  const [feedbackError, setFeedbackError] = useState<string | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isMaskVisible, setIsMaskVisible] = useState(false);

  // モーダルを開きメッセージを設定
  const showMessageModal = (newMessage: string) => {
    setFeedbackMsg(newMessage);
    setFeedbackError(null); // エラークリア
    setIsModalVisible(true); // モーダルを開く
  };
  // モーダルを開きエラーを設定
  const showErrorModal = (newError: string) => {
    setFeedbackError(newError);
    setFeedbackMsg(null); // メッセージクリア
    setIsModalVisible(true); // モーダルを開く
  };
  const [isConfirmModalVisible, setIsConfirmModalVisible] = useState(false);
  const [confirmMsg, setConfirmMsg] = useState<string | null>(null);

  const handleConfirmModalAction = async () => {
    getSession();
    setIsConfirmModalVisible(false);
    setIsMaskVisible(true);
    const selectedItems = allCalendars.filter((calendar) =>
      selectedCalendars.has(calendar.ID),
    );
    const response = await fetch("/api/calendars/copy", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ selectedCalendarId, selectedItems }),
    });

    if (response.status === 401) {
      router.push("/login");
      return;
    } else {
      const res = await response.json();
      if (res.status === 200) {
        showMessageModal(
          PCSD_ERROR_MESSAGES.EMEC0015.replace(
            "{0}",
            "クライアント動作設定のコピー",
          ),
        );
      } else {
        const errorMessage = res.error;

        if (errorMessage) {
          showErrorModal(errorMessage);
        } else {
          showErrorModal(
            formatMessage(PCSD_ERROR_MESSAGES.EMEC0014, [
              "クライアント動作設定のコピー",
            ]),
          );
        }
      }
      setIsMaskVisible(false);
    }
  };

  // 確認モーダルを閉じる
  const closeConfirmModal = () => {
    setIsConfirmModalVisible(false);
  };

  return (
    <div
      id="calendar-modal"
      tabIndex={-1}
      className="hidden overflow-y-auto overflow-x-hidden fixed inset-0 z-50 flex justify-center items-center"
    >
      {isConfirmModalVisible && (
        <ConfirmModal
          message={confirmMsg}
          onAction={handleConfirmModalAction}
          onClose={closeConfirmModal}
        />
      )}
      {isModalVisible && (
        <FeedbackModal
          message={feedbackMsg}
          error={feedbackError}
          onClose={closeModal}
        />
      )}
      {isMaskVisible && (
        <div className="fixed inset-0 bg-gray-900 opacity-50 z-40"></div>
      )}
      <div className="w-full max-w-3xl lg:max-w-2xl xl:max-w-4xl 2xl:max-w-6xl max-h-full">
        <div className="rounded shadow bg-gray-600">
          <div className="flex items-center justify-between whitespace-nowrap p-4 border-b rounded-t bg-gradient-header">
            <h3 className="text-lg font-semibold text-white">
              クライアント動作設定コピー先選択
            </h3>
            <div className="min-w-16 ml-4 mr-2 flex-grow text-sm font-semibold text-white">
              <Tooltip text={selectedCalendarName} torb="bottom">
                <p className="truncate">
                  コピー元：{sanitize(selectedCalendarName)}
                </p>
              </Tooltip>
            </div>
            <button
              type="button"
              className="flex-shrink-0 text-gray-400 bg-transparent rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center hover:bg-gray-600 hover:text-white"
              onClick={closeModalWithoutNav}
            >
              <svg
                className="w-3 h-3"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 14 14"
              >
                <path
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                />
              </svg>
              <span className="sr-only">Close modal</span>
            </button>
          </div>
          <div className="h-96 p-4 space-y-4 bg-white text-sm font-medium overflow-y-auto">
            <table className="w-full text-left text-sm text-gray-500 table-auto">
              <thead className="bg-gray-50 text-xs text-gray-700">
                <tr>
                  <th key="id" scope="col" className="px-6 py-3 border-l">
                    ＃
                  </th>
                  <th key="chkb" scope="col" className="px-6 py-3 border-l">
                    <div>
                      <input
                        type="checkbox"
                        checked={selectAll}
                        onChange={handleSelectAll}
                      />
                    </div>
                  </th>
                  <th
                    key="groupname"
                    scope="col"
                    className="px-6 py-3 border-l"
                  >
                    カレンダーグループ名
                  </th>
                </tr>
              </thead>
              <tbody>
                {allCalendars?.length !== 0
                  ? allCalendars!.map((calendar: any) =>
                      calendar.ID !== selectedCalendarId ? (
                        <tr
                          key={calendar.ID}
                          className="border-b odd:bg-white even:bg-gray-50"
                        >
                          <td className="whitespace-nowrap py-3 pl-6 pr-3 border-r">
                            {(() => {
                              rowNum = rowNum + 1;
                              return rowNum;
                            })()}
                          </td>
                          <td className="whitespace-nowrap py-3 pl-6 pr-3 border-r">
                            <input
                              type="checkbox"
                              checked={selectedCalendars.has(calendar.ID)}
                              onChange={() => handleCheckboxChange(calendar.ID)}
                            />
                          </td>
                          <td className="whitespace-nowrap py-3 pl-6 pr-3 border-r">
                            {sanitize(calendar.GROUP_NAME)}
                          </td>
                        </tr>
                      ) : null,
                    )
                  : null}
              </tbody>
            </table>
          </div>

          <div className="flex justify-end items-center p-4 border-t rounded-b bg-gradient-header gap-3">
            <button
              onClick={copyIniDat}
              type="button"
              className={`w-28 h-8 ms-3 rounded px-3 py-2 text-center text-xs font-medium text-transparent 
               transition duration-300 
              ${
                selectedCalendars.size === 0 // コピー先を選択した場合、「登録」が有効化される
                  ? "bg-gray-400 cursor-not-allowed opacity-50" // disabled css
                  : "bg-gradient-blue hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80" // normal css
              }`}
              disabled={selectedCalendars.size === 0}
              style={{ backgroundImage: "url(/icons/btn_ok.ico)" }}
            >
              OK
            </button>
            <button
              type="button"
              className="w-28 rounded bg-gradient-dark px-3 py-2 text-center text-xs font-medium text-transparent 
                  drop-shadow-dark shadow-dark hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
              onClick={closeModalWithoutNav}
              style={{ backgroundImage: "url(/icons/btn_cancel.ico)" }}
            >
              キャンセル
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Modal;
