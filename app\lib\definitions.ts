/**
 * @file definitions.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

// This file contains type definitions for your data.
// It describes the shape of the data, and what data type each property should accept.
// For simplicity of teaching, we're manually defining these types.

//#region PCSD add
export const navLinks = [
  {
    name: "画面",
    key: "page",
    subs: [
      {
        name: "カレンダー",
        href: "/dashboard/calendars",
        breadName: "カレンダー一覧",
      },
      { name: "利用者環境ログ", href: "/dashboard/userlogs" },
      { name: "タスク一覧", href: "/dashboard/tasks" },
      { name: "ソフトウェアダウンロード", href: "/dashboard/softwares" },
      { name: "ワークフロー", href: "", target: "_blank" },
    ],
  },
];
export const otherLinks = [
  {
    name: "カレンダー",
    key: "calendars",
    subs: [
      { name: "カレンダー登録", href: "/dashboard/calendars/create" },
      {
        name: "カレンダー登録",
        href: "/dashboard/calendars/[id]/[groupname]/update",
      },
      { name: "クライアント設定", href: "/dashboard/calendars/[id]/edit" },
    ],
  },
];

export const navLinks2 = [
  {
    name: "カレンダー",
    key: "calendars",
    href: "/dashboard/calendars",
  },
  {
    name: "利用者環境ログ",
    key: "userlogs",
    href: "/dashboard/userlogs",
  },
  {
    name: "タスク一覧",
    key: "tasks",
    href: "/dashboard/tasks",
  },
  {
    name: "ソフトウェアダウンロード",
    key: "softwares",
    href: "/dashboard/softwares",
  },
  {
    name: "ワークフロー",
    key: "workflow",
    href: "",
    target: "_blank",
  },
];

export const PC_MODES = [
  {
    key: "SHUTDOWN",
    value: "シャットダウン",
  },
  {
    key: "SHUTDOWN_F",
    value: "強制シャットダウン",
  },
  {
    key: "LOGOFF",
    value: "ログオフ",
  },
  {
    key: "LOGOFF_F",
    value: "強制ログオフ",
  },
  {
    key: "LOCK_SCREEN",
    value: "ロック画面表示",
  },
  {
    key: "WARNING",
    value: "警告表示",
  },
  {
    key: "NOTHING",
    value: "何もしない",
  },
];

export const BASE_CALENDAR_GROUP_NAME = "基本カレンダー";

export const allowedUploadCalendarFileNames = [
  "OTWWTCAL.xml",
  "OTWCALEX01.xml",
  "OTWCALEX02.xml",
  "OTWCALEX03.xml",
  "OTWCALEX04.xml",
];

export const softwareFilePath = ["PACK", "ONLY", "DOCUMENT"];

export interface HashMap {
  [key: string]: string[];
}

export const SJIS_ENCODING = "Shift_JIS";

export const UTF8_ENCODING = "utf-8";

export const UPLOAD_PATH = "uploads";

export const UPLOAD_TAR_PATH = "zips";

export const AS_TAR_FILE_PREFIX = "AsTarFile";

export const AS_TAR_COMMAND_PATH = "cmd";
export const AS_TAR_COMMAND =
  process.env.NODE_ENV === "development" ? "as_tar.exe" : "as_tar";

export const OVERTIME_TEMPLATE_PATH = "templates";
export const OVERTIME_TEMPLATE = "overtime_template.ini";

export const FOLDER_DIVIDE_BASE = 10000;
export const AZURE_STORAGE_SAS_TTL_SECONDS = 7200;
//#endregion PCSD add

export const ENV = {
  APP_CACHE_TTL_SECONDS: process.env.APP_CACHE_TTL_SECONDS
    ? Number(process.env.APP_CACHE_TTL_SECONDS)
    : 7200,
  AZURE_STORAGE_CONNECTION_STRING:
    process.env.AZURE_STORAGE_CONNECTION_STRING || "",
  JWT_MAX_AGE_SECONDS: process.env.JWT_MAX_AGE_SECONDS
    ? Number(process.env.JWT_MAX_AGE_SECONDS)
    : 1800,
  LOG_LEVEL: process.env.LOG_LEVEL || "info",
  PCSD_FILE_PATH: process.env.PCSD_FILE_PATH || "",
  USER_LOG_FILE_PATH: process.env.USER_LOG_FILE_PATH || "",
  SOFTWAREA_FILE_PATH: process.env.SOFTWAREA_FILE_PATH || "",
  USER_WIN_LOG_STR: process.env.USER_WIN_LOG_STR || "",
  USER_LOG_FILE_NAME: process.env.USER_LOG_FILE_NAME || "",
  KEYCLOAK_PUBLIC_DOMAIN_NAME: process.env.KEYCLOAK_PUBLIC_DOMAIN_NAME,
  KEYCLOAK_INTERNAL_DOMAIN_NAME: process.env.KEYCLOAK_INTERNAL_DOMAIN_NAME,
  KEYCLOAK_REALM: process.env.KEYCLOAK_REALM,
  KEYCLOAK_CLIENT: process.env.KEYCLOAK_CLIENT,
  KEYCLOAK_REDIRECT_URL: process.env.KEYCLOAK_REDIRECT_URL,
  KEYCLOAK_CLIENT_SECRET: process.env.KEYCLOAK_CLIENT_SECRET,
  PGSQL_HOST: process.env.PGSQL_HOST,
  PGSQL_PORT: process.env.PGSQL_PORT,
  PGSQL_DATABASE_WORKFLOW: process.env.PGSQL_DATABASE_WORKFLOW,
  AZURE_STORAGE_LOG_CONTAINER_NAME:
    process.env.AZURE_STORAGE_LOG_CONTAINER_NAME || "",
  AZURE_STORAGE_LOG_BASE_PATH: process.env.AZURE_STORAGE_LOG_BASE_PATH || "",
  AZURE_STORAGE_TEMP_ZIP_CONTAINER_NAME:
    process.env.AZURE_STORAGE_TEMP_ZIP_CONTAINER_NAME || "",
  AZURE_STORAGE_TEMP_ZIP_BASE_PATH:
    process.env.AZURE_STORAGE_TEMP_ZIP_BASE_PATH || "",
  AZURE_FUNCTION_URL: process.env.AZURE_FUNCTION_URL || "",
  AZURE_FUNCTION_KEY: process.env.AZURE_FUNCTION_KEY || "",
};

export const PCSD_ERROR_MESSAGES = {
  EMEC0001: "{0}が正しくありません。",
  EMEC0002:
    "新しいパスワードには8文字以上、128文字以下のパスワードを入力してください。",
  EMEC0003:
    "新しいパスワードには2種類以上の文字の組み合わせで入力してください。",
  EMEC0004: "{0}の文字列が{1}と同じです。",
  EMEC0005: "{0}を指定してください。",
  EMEC0006:
    "データベースに一時的に接続できません。しばらくしてから再度ポータルにアクセスしてください。",
  EMEC0007: "同じカレンダーグループ名がすでに存在しています。",
  EMEC0008: "{0}に使用できない文字が含まれています。",
  EMEC0009: "６つのファイルを選択してください。",
  EMEC0010: "許可されたファイルを選択してください。",
  EMEC0011: "有効なxmlファイルを指定してください。",
  EMEC0012: "ファイルのサイズが制限（{0}）を超えました。",
  EMEC0013:
    "{0}は{1}行以内、1行あたりの文字数は{2}文字以内で入力してください。",
  EMEC0014: "{0}に失敗しました。",
  EMEC0015: "{0}に成功しました。",
  EMEC0017: "現在ポータルは利用できません。",
  EMEC0018:
    "サーバーに一時的に接続できません。しばらくしてから再度ポータルにアクセスしてください。",
  EMEC0019: "選択したコピー先の数が上限値（{0}）を超えました。",
  EMEC0020: "ただいま他の圧縮処理が実行中です。しばらく経ってから再度お試しください。",
  EMEC0021: "ログ情報が存在しません。",
  EMEC0022: "ダウンロードするログを選択してください。",
};

export const PCSD_INFO_MESSAGES = {
  IMEC0001: "クライアント動作設定をコピーしますか？",
  IMEC0002: "現在の値をデフォルトで上書きしますか？",
  IMEC0003:
    // 改行を保持する必要があるため、CSS: white-space: pre-line; を指定する必要があります。
    "保存されていない設定は破棄されます。\nカレンダー一覧画面に戻りますか？",
  IMEC0004: "基本カレンダーと同じ設定に戻しますか？",
  IMEC0005: "カレンダーを削除しますか？",
  IMEC0006: "ファイルを圧縮中です。しばらくお待ちください...",
  IMEC0007: "ログファイルを準備中です。圧縮タスクの最新状態をタスク一覧画面で確認してください。圧縮完了したら、タスク一覧画面でダウンロードできます。\nタスク名：{0}",
};

export interface ModalProps {
  isOpen?: boolean;
  onClose?: () => void;
  onOK?: (message: string) => void;
  onError?: (error: string) => void;
}

export const atLeastTwoKindsRegex = new RegExp(
  /^(?:(?=(?:.*[A-Z]))(?=(?:.*[a-z]))|(?=(?:.*[A-Z]))(?=(?:.*\d))|(?=(?:.*[A-Z]))(?=(?:.*[$@!%*#?& "'()+,-./:;=<>\[\]\\^_`{|}~]))|(?=(?:.*[a-z]))(?=(?:.*\d))|(?=(?:.*[a-z]))(?=(?:.*[$@!%*#?& "'()+,-./:;=<>\[\]\\^_`{|}~]))|(?=(?:.*\d))(?=(?:.*[$@!%*#?& "'()+,-./:;=<>\[\]\\^_`{|}~]))).+$/,
);
export const IsNumber = new RegExp(/^\d+$/);
export const LoginFormUserIdPattern = new RegExp(/^[a-z0-9.]+$/);
export const LoginFormPasswordPattern = new RegExp(
  /^[A-Za-z0-9\d$@!%*#?& "'()+,-./:;=<>\[\]\\^_`{|}~]+$/,
);
export const UserLogsSearchPattern = new RegExp(/[^A-Za-z0-9_]/g);
export const VALID_PAGE_SIZES = [10, 30, 50];
