/**
 * @file page.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */
"use client";

import { FOLDER_DIVIDE_BASE, PCSD_INFO_MESSAGES } from "@/app/lib/definitions";
import FeedbackModal from "@/app/ui/feedback-modal";
import Thead from "@/app/ui/thead";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { Suspense, useEffect, useState } from "react";

const createSubfolders = (userIdsArray: string[], folderDivideBase: number) => {
  const subfoldercnt = userIdsArray.length;
  const subfolders: string[] = [];
  const totalRanges = Math.ceil(subfoldercnt / folderDivideBase);

  for (let i = 0; i < totalRanges; i++) {
    const start = i * folderDivideBase + 1;
    const end = Math.min((i + 1) * folderDivideBase, subfoldercnt);
    subfolders.push(`${start}-${end} 件`);
  }

  return subfolders;
};

const downloadFile = (blob: Blob, fileName: string) => {
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = fileName;
  document.body.appendChild(a);
  a.click();
  a.remove();
};

export default function Page() {
  const [userIdsArray, setUserIdsArray] = useState<string[]>([]);
  const [downloadingMsg, setDownloadingMsg] = useState("");
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [feedbackMsg, setFeedbackMsg] = useState<string | null>(null); // メッセージ用の状態
  const [feedbackError, setFeedbackError] = useState<string | null>(null); // エラー用の状態
  const [isModalVisible, setIsModalVisible] = useState(false); // モーダルの表示状態
  const router = useRouter();

  useEffect(() => {
    const allUserids = sessionStorage.getItem("allUserids");
    const parsedIds = allUserids ? JSON.parse(allUserids) : [];
    setUserIdsArray(parsedIds);
  }, []);

  // サブフォルダ範囲を生成する
  const subfolders = createSubfolders(userIdsArray, FOLDER_DIVIDE_BASE);
  const handleDownloadClick = async (index: number) => {
    setDownloadingMsg("");
    setDownloadLoading(true);

    // 現在のダウンロード範囲を計算
    const start = index * FOLDER_DIVIDE_BASE + 1;
    const end = Math.min((index + 1) * FOLDER_DIVIDE_BASE, userIdsArray.length);

    // サーバーにリクエストを送信してユーザーログをダウンロード
    const response = await fetch("/api/userlogs/download", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        batchdownload: index,
        subfoldercnt: userIdsArray.length,
        userIds: userIdsArray.slice(start - 1, end),
      }),
    });

    // 権限チェック
    if (response.status === 401) {
      setDownloadLoading(false);
      router.push("/login");
      return;
    } else if (response.status === 404 || response.status === 500) {
      const res = await response.json();
      setDownloadLoading(false);
      showErrorModal(res.error);
      return;
    } else {
      // 正常にファイルをダウンロードした場合
      setDownloadingMsg(`${PCSD_INFO_MESSAGES.IMEC0006}`);
      const blob = await response.blob(); // Blobデータを取得
      setDownloadLoading(false); // ローディング状態を終了
      downloadFile(blob, `${subfolders[index]}.zip`); // ダウンロード関数を呼び出す
    }
  };

  const bounceAnimation = `
  @keyframes typing {
    from {
      width: 0;
    }
    to {
      width: 100%;
    }
  }
  @keyframes blink {
    from, to {
      border-color: transparent;
    }
    50% {
      border-color: black;
    }
  }
`;

  const styles = {
    container: {
      backgroundColor: "white",
      padding: "20px",
      borderRadius: "10px",
      textAlign: "center" as const,
      animation: "typing 2s steps(30, end), blink 0.75s step-end infinite",
    } as React.CSSProperties,
    modal: {
      position: "fixed",
      top: "0",
      left: "0",
      width: "100%",
      height: "100%",
      backgroundColor: "rgba(0, 0, 0, 0.5)",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      zIndex: 1000,
    } as React.CSSProperties,
    modalContent: {
      backgroundColor: "white",
      padding: "20px",
      borderRadius: "10px",
      textAlign: "center",
    } as React.CSSProperties,
  };

  // モーダルを開きエラーを設定
  const showErrorModal = (newError: string) => {
    setFeedbackError(newError);
    setFeedbackMsg(null); // メッセージクリア
    setIsModalVisible(true); // モーダルを開く
  };
  // モーダルを閉じる
  const closeModal = () => {
    setIsModalVisible(false);
    setFeedbackError(null); // メッセージをクリア
    setFeedbackMsg(null); // エラーをクリア
  };

  return (
    <div className="p-4 h-full flex flex-col space-y-3">
      {isModalVisible && (
        <FeedbackModal
          message={feedbackMsg}
          error={feedbackError}
          onClose={closeModal}
        />
      )}
      <div className="text-base">
        <div className="flex items-center justify-between"></div>
      </div>
      <div className="text-base">
        <div className="flex items-center justify-between">
          <div className="flex-column flex flex-wrap items-center justify-between rounded-t-xl"></div>
        </div>
      </div>

      <div
        className={`relative overflow-x-hidden shadow-md rounded-t-lg overflow-y-auto`}
      >
        <table className="table-auto whitespace-nowrap w-full h-full text-left text-sm text-gray-500">
          <Suspense fallback={<div>Loading...</div>}>
            <Thead
              headers={[
                { key: "no", label: "＃", css: "w-[60px]", isNotSort: true },
                { key: "userid", label: "利用者環境ログ", isNotSort: true },
                { key: "download", label: "ダウンロード", isNotSort: true },
              ]}
              defaultOrder="userid"
              defaultSort="asc"
            />
          </Suspense>
          <tbody>
            {subfolders?.length !== 0 ? (
              subfolders.map((entry, index) => {
                return (
                  <tr
                    key={index}
                    className="border-b odd:bg-white even:bg-gray-50"
                  >
                    <td className="w-[60px] overflow-hidden text-ellipsis whitespace-nowrap py-3 pl-6 pr-3 border-r">
                      {index + 1}
                    </td>
                    <td className="border-r px-6 py-4">{entry}</td>
                    <td className="whitespace-nowrap py-3 pl-6 pr-3 border-r">
                      <div className="flex items-center gap-3">
                        <Image
                          src="/icons/download.ico"
                          width={32}
                          height={32}
                          alt="download"
                          style={{ cursor: "pointer" }}
                          onClick={() => handleDownloadClick(index)}
                        />
                      </div>
                    </td>
                  </tr>
                );
              })
            ) : (
              <tr>
                <td>
                  <div className="p-4"></div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
        {downloadLoading && (
          <div style={styles.modal}>
            {downloadingMsg === "" ? (
              ""
            ) : (
              <div style={styles.modalContent}>
                <h2>{downloadingMsg}</h2>
              </div>
            )}
          </div>
        )}
        <style>{bounceAnimation}</style>
      </div>
    </div>
  );
}
