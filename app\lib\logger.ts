/**
 * @file logger.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import winston from "winston";
import { ENV } from "./definitions";

// 関数のシグネチャをログとして取得するユーティリティ関数
const logFunctionSignature = (func: Function, args: any[]) => {
  const funcName = func.name;

  const paramNames =
    func
      .toString()
      .replace(/\/\*.*?\*\//g, "")
      .match(/\((.*?)\)/)?.[1]
      .split(",")
      .map((param) => param.trim()) || [];

  const logObject: { [key: string]: any } = {
    funcName,
  };

  paramNames.forEach((param, index) => {
    logObject[`args.${param}`] = args[index];
  });

  return logObject;
};

// Winstonのロガーの設定
const logger = winston.createLogger({
  level: ENV.LOG_LEVEL,
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format((info) => {
      const { requestId, timestamp, level, message, ...rest } = info;
      return {
        requestId,
        timestamp,
        level,
        message,
        ...rest,
      };
    })(),
    winston.format.json({ deterministic: false }),
  ),
  transports: [new winston.transports.Console()],
});

// ログレベル指定可能な関数のデコレーターのファクトリー関数
export const LogFunctionSignature =
  (logLevel: string = "info") =>
  (
    target: Object,
    propertyKey: string | symbol,
    descriptor: PropertyDescriptor,
  ) => {
    const originalMethod = descriptor.value;

    descriptor.value = function (...args: any[]) {
      const logMessage = logFunctionSignature(originalMethod, args);
      const { funcName, ...rest } = logMessage;

      // ログを出力
      logger.log({
        level: logLevel,
        message: funcName,
        ...rest,
      });

      return originalMethod.apply(this, args);
    };

    return descriptor;
  };

/**
 * ロガーのクラス
 */
class Logger {
  // ログを出力する汎用関数
  static log(level: string, logObject: { [key: string]: any } | string) {
    if (typeof logObject === "string") {
      logger.log({ level, message: logObject });
    } else {
      logger.log({ level, message: "", ...logObject });
    }
  }

  // 情報レベルのログを出力する関数
  static info(logObject: { [key: string]: any } | string) {
    Logger.log("info", logObject);
  }

  // デバッグレベルのログを出力する関数
  static debug(logObject: { [key: string]: any } | string) {
    Logger.log("debug", logObject);
  }

  // エラーレベルのログを出力する関数
  static error(logObject: { [key: string]: any } | string) {
    Logger.log("error", logObject);
  }
}

export default Logger;
