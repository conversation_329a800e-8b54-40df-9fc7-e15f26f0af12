/**
 * @file page.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import Form from "@/app/ui/calendars/create-form";

export default async function Page({
  params,
  searchParams,
}: {
  params: { id: string; groupname: string };
  searchParams: { page: number };
}) {
  const id = params.id;
  const groupname = params.groupname;
  const page = searchParams.page;

  return (
    <div className="p-4 h-full flex flex-col space-y-3 overflow-y-auto overflow-hidden">
      <Form id={id} groupname={groupname} page={page} />
    </div>
  );
}
