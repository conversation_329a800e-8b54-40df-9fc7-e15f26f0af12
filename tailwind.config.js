/**
 * @file tailwind.config.js
 * @description 
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

module.exports = {
  darkMode: 'class',
  content: [
    "./app/**/*.{js,ts,jsx,tsx}",
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
    "./node_modules/flowbite/**/*.js",
    "./node_modules/flowbite-react/**/*.js",
  ],
  theme: {
    fontFamily: {
      body: [
        'Hiragino Sans',
        'ヒラギノ角ゴシック',
        'メイリオ',
        'Meiryo',
        'MS Ｐゴシック',
        'MS PGothic',
        'sans-serif',
        'YuGothic',
        'Yu Gothic',
      ],
    },
    extend: {
      backgroundImage: {
        'gradient-blue': 'linear-gradient(0deg, #4a6c9b 0%, #486a9a 50%, #5877a2 51%, #889fbd 100%)',
        'gradient-dark': 'linear-gradient(0deg, #454b53 0%, #2b313b 50%, #353d45 51%, #6d7177 100%)',
        'gradient-light': 'linear-gradient(0deg, #bfc1c3 0%, #bfc1c3 50%, #d1d3d5 51%, #f5f5f5 100%)',
        'gradient-header': 'linear-gradient(0deg, #00000060 0%, #00000060 50%, #15151560 50%, #47474760 100%)',
        'gradient-bg': 'linear-gradient(0deg, #000000, #47474760)',
      },
      boxShadow: {
        'dark': 'inset 0px 2px 1px #231f20',
        'blue': 'inset 0px 2px 1px rgba(0,0,0,0.7)',
        'light': 'inset 0px 1px 1px rgba(255,255,255,0.75)',
      },
      dropShadow: {
        'dark': '0px 1px 1px rgba(255,255,255,0.45)',
        'blue': '0px 1px 0px rgba(255,255,255,0.36)',
        'light': '0px 2px 1px rgba(0,0,0,0.75)',
      },
      zIndex: {
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100',
      }
    },
  },
  plugins: [require('flowbite/plugin')],
};
