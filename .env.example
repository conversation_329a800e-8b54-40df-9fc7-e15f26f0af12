# Database Configuration
# Prisma documentation: https://www.prisma.io/docs/concepts/database-connectors/sql-server
PGSQL_HOST=your-database-host
PGSQL_PORT=your-database-port
PGSQL_DATABASE_WORKFLOW=workflowDB_main

# Prisma connection URL for PostgreSQL
PGSQL_PRISMA_DEFAULT_URL=postgresql://{user}:{pwd}@{URL}:{port}/postgres

# Authentication Settings
# JWT_MAX_AGE_SECONDS: The maximum duration (in seconds) before a JWT expires
JWT_MAX_AGE_SECONDS=1800

# Logging Configuration
# LOG_LEVEL: Defines the logging level (e.g., info, debug, warn, error)
LOG_LEVEL=info

# Cache Settings
# APP_CACHE_TTL_SECONDS: Cache expiration time in seconds
APP_CACHE_TTL_SECONDS=7200

# File Paths[Azure File Info]
PCSD_FILE_PATH=XXX
USER_LOG_FILE_PATH=customer
SOFTWAREA_FILE_PATH=media
USER_WIN_LOG_STR=userwinlog
USER_LOG_FILE_NAME=userwinlog.csv

# Keycloak Configuration
KEYCLOAK_PUBLIC_DOMAIN_NAME=http://keycloak.prod.{IP}.nip.io
KEYCLOAK_INTERNAL_DOMAIN_NAME=http://keycloak.prod.{IP}.nip.io
KEYCLOAK_REALM=pcsd
KEYCLOAK_CLIENT=public-web
KEYCLOAK_REDIRECT_URL=http://{IP}:{port}/callback
KEYCLOAK_CLIENT_SECRET={SECRET_Key}

# Azure Storage
# AZURE_STORAGE_CONNECTION_STRING: Connection string for Azure Blob Storage
AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=jp1blob;AccountKey=your-account-key;EndpointSuffix=core.windows.net

# Next.js Public Variables
# NEXT_PUBLIC_WORKFLOW_CONSTANT_LINK: Public URL for workflow
NEXT_PUBLIC_WORKFLOW_CONSTANT_LINK=pcsd-xxx.com/workflow