/**
 * @file route.ts
 * @description セッション情報APIルート
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { cookies } from "next/headers";
import { getIronSession } from "iron-session";
import { defaultSession, sessionOptions, SessionData } from "@/app/lib/session";
import { ENV } from "@/app/lib/definitions";

/**
 * GETリクエストを処理し、セッション情報を返す。
 * セッションが存在しない場合、デフォルトのセッション情報を返す。
 */
export async function GET() {
  // リクエストヘッダーからクッキーを取得し、IronSessionを初期化する
  const session = await getIronSession<SessionData>(cookies(), sessionOptions);

  // セッションにユーザー情報が存在しない場合、デフォルトのセッション情報を返す
  if (!session.user) {
    return Response.json(defaultSession);
  }

  // ローカルセッションの有効期限の更新
  session.updateConfig({
    ...sessionOptions,
    cookieOptions: {
      ...sessionOptions.cookieOptions,
      expires: undefined,
      maxAge: ENV.JWT_MAX_AGE_SECONDS,
    },
  });

  await session.save();

  // refreshTokenを除外したセッション情報をコピーする
  const sessionWithoutRefreshToken = { user: { ...session.user, refreshToken: undefined, schemaPwd: undefined } };

  // 除外したセッション情報をJSON形式で返す
  return Response.json(sessionWithoutRefreshToken);
}
