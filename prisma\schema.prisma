// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("PGSQL_PRISMA_DEFAULT_URL") // uses connection pooling
}

model SOFTWARE_DOWNLOAD_INFO {
  ID                    String         @id @default(uuid()) @db.Uuid
  SOFTWARE_NAME         String         @db.VarChar(256)
  VERSION_INFO          String         @db.VarChar(256)
  SOFTWARE_UPDATE_DATE  DateTime        
  NEW_MARK_ADD_FLAG     Boolean        
  SOFTWARE_OVERVIEW     String?
  SOFT_PACK_FILEPATH    String?        @db.VarChar(256)         
  SOFT_ONLY_FILEPATH    String?        @db.VarChar(256)
  DOCMENT_FILEPATH      String?        @db.VarChar(256)
  UPDATE_DATE           DateTime
  UPDATE_USER           String         @db.Var<PERSON>har(256)
  ENABLED_FLAG          String         @db.Bit(1)  
}

model CUSTOMER_SOFTWARE_DOWNLOAD {
  ID                    String         @id @default(uuid()) @db.Uuid
  SOFTWARE_NAME         String         @db.VarChar(256)
  VERSION_INFO          String         @db.VarChar(256)
  SOFTWARE_UPDATE_DATE  DateTime 
  UPDATE_DATE           DateTime
  UPDATE_USER           String         @db.VarChar(256)
}

model SCHEMA_USER {
  ID                    String         @id @default(uuid()) @db.Uuid
  SCHEMA_NAME           String         @db.VarChar(256)
  SCHEMA_USER           String         @db.VarChar(256)
  SCHEMA_PWD            String         @db.VarChar(256)
  UPDATE_DATE           DateTime
  UPDATE_USER           String         @db.VarChar(256)
}