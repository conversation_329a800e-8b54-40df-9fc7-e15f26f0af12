/**
 * @file session.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { SessionOptions } from "iron-session";
import { ENV } from "./definitions";

export interface SessionData {
  user: {
    id: string,
    userId: string,
    tz: string,
    refreshToken: string,
    schemaName: string,
    schemaUser: string,
    schemaPwd: string,
  },
}

export const defaultSession: SessionData = {
  user: {
    id: "",
    userId: "",
    tz: "",
    refreshToken: "",
    schemaName: "",
    schemaUser: "",
    schemaPwd: "",
  },
};

export const sessionOptions: SessionOptions = {
  password: "complex_password_at_least_32_characters_long",
  cookieName: "portal_keycloak_callback_session",
  cookieOptions: {
    secure: process.env.NODE_ENV === "production",
    httpOnly: true,  // クライアント・スクリプトによるクッキーへのアクセスの防止
    sameSite: "lax", // CSRF攻撃の防止
    maxAge: ENV.JWT_MAX_AGE_SECONDS,
  },
};
