﻿/**
 * @file route.ts
 * @description ユーザーログの圧縮タスクを新規作成するためのAPIエンドポイント。
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { NextRequest, NextResponse } from "next/server";
import { fetchUserhavelogs, fetchAvailableUserlogsCount } from "@/app/lib/data/userlogs";
import { TasksService } from "@/app/lib/services/tasks";
import { ENV, FOLDER_DIVIDE_BASE, PCSD_ERROR_MESSAGES, PCSD_INFO_MESSAGES } from "@/app/lib/definitions";
import { handleApiError } from "@/app/lib/portal-error";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";
import { SessionData, sessionOptions } from "@/app/lib/session";
import { fetchtaskCompressingCnt } from "@/app/lib/data/tasks";
import { formatMessage } from "@/app/lib/utils";

/**
 * ログ圧縮処理を実行するAzure Functionを非同期で呼び出します。
 * この関数は「Fire and Forget」方式で動作し、完了を待ちません。
 * @param taskId 処理対象のタスクID
 */
function triggerCompressionFunction(taskId: string) {
  const functionUrl = ENV.AZURE_FUNCTION_URL;
  const functionKey = ENV.AZURE_FUNCTION_KEY;

  if (!functionUrl) {
    console.error("環境変数にAzure FunctionのURLが設定されていません。");
    return;
  }

  console.log(`Azure Functionを呼び出します。taskId: ${taskId}`);
  fetch(functionUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "x-functions-key": functionKey || "",
    },
    body: JSON.stringify({ taskId: taskId }),
  }).catch((error) => {
    // ネットワークエラーなどでリクエスト自体が失敗した場合のログ
    console.error(
      `Azure Functionの呼び出しに失敗しました。taskId: ${taskId}, Error:`,
      error,
    );
  });
}

/**
 * ログ圧縮タスク作成リクエストを処理するPOSTハンドラ
 * @param request APIリクエストオブジェクト
 */
export async function POST(request: NextRequest) {
  try {
    // 1. リクエスト情報の取得
    const { TASK_TYPE, PARAMETERS } = await request.json();
    const session = await getIronSession<SessionData>(
      cookies(),
      sessionOptions,
    );
    const INSERT_REQUESTING_USER_ID = session.user.userId;
    // 対象のディレクトリのパスを構築
    const schemaName = session.user.schemaName;
    const INSERT_SCHEMA_NAME = session.user.schemaName;

    // 2. 実行中の圧縮タスクの有無をチェック
    const taskCompressingCnt = await fetchtaskCompressingCnt();
    if ((taskCompressingCnt ?? 0) > 0) {
      return NextResponse.json(
        {
          error: PCSD_ERROR_MESSAGES.EMEC0020,
        },
        { status: 404 }, // 404 Conflict: リソースの競合
      );
    }

    // 3. 処理対象パラメータと合計ファイル数を決定
    let finalParameters: string[];
    let totalFileCount: number;

    // PARAMETERSが空の場合、「全件ダウンロード」と見なす
    if (!PARAMETERS || PARAMETERS.length === 0) {
      const usersFromDb = await fetchUserhavelogs(schemaName);
      if (Array.isArray(usersFromDb) && usersFromDb.length > 0) {
        finalParameters = usersFromDb.map((user) => user.USER_ID);
        totalFileCount = await fetchAvailableUserlogsCount(schemaName) || 0;
      } else {
        // DBからユーザーが見つからない場合
        return NextResponse.json(
          { error: "圧縮対象となるログファイルが見つかりませんでした。" },
          { status: 404 },
        );
      }
    } else {
      // PARAMETERSが存在する場合、「選択ダウンロード」と見なす
      finalParameters = PARAMETERS;
      totalFileCount = PARAMETERS.length;
    }

    // 最終的な処理対象ファイル数が0件の場合、処理を終了
    if (totalFileCount === 0) {
      return NextResponse.json(
        { error: "圧縮対象となるログファイルが一件も見つかりませんでした。" },
        { status: 404 },
      );
    }

    // 4. タスク種別に応じてタスクを登録
    // 「全件ダウンロード」の場合の処理
    if (TASK_TYPE !== "SELECTED") {
      // ファイル数が分割基準を超える場合、タスクを分割して登録
      if (totalFileCount > FOLDER_DIVIDE_BASE) {
        const taskTypeMulti = "ALL_MULTI";
        const numberOfTasks = Math.ceil(totalFileCount / FOLDER_DIVIDE_BASE);

        // 分割されたパラメータ配列を準備
        const parametersArray: string[] = [];
        for (let i = 0; i < numberOfTasks; i++) {
          const startIndex = i * FOLDER_DIVIDE_BASE;
          const endIndex = startIndex + FOLDER_DIVIDE_BASE;
          const taskParameters = finalParameters.slice(startIndex, endIndex);
          parametersArray.push(JSON.stringify(taskParameters));
        }

        try {
          const result = await TasksService.createMultipleTasks({
            schemaName: INSERT_SCHEMA_NAME,
            requestingUserId: INSERT_REQUESTING_USER_ID,
            taskType: taskTypeMulti,
            parametersArray,
            status: "PENDING",
            totalFileCount,
            folderDivideBase: FOLDER_DIVIDE_BASE,
          });

          // 非同期でAzure Functionを呼び出す
          result.tasks.forEach(task => {
            triggerCompressionFunction(task.taskId);
          });

          const message = formatMessage(PCSD_INFO_MESSAGES.IMEC0007, [result.baseTaskName]);
          return NextResponse.json({ message: message }, { status: 200 });
        } catch (error) {
          console.error("タスクの作成中にエラーが発生しました:", error);
          return NextResponse.json(
            { error: "タスクの作成中にエラーが発生しました。" },
            { status: 500 },
          );
        }
      } else {
        // ファイル数が基準以内の場合、単一タスクとして登録
        const INSERT_TASK_TYPE = "ALL_SINGLE";

        const createdTask = await TasksService.createTask({
          schemaName: INSERT_SCHEMA_NAME,
          requestingUserId: INSERT_REQUESTING_USER_ID,
          taskType: INSERT_TASK_TYPE,
          parameters: JSON.stringify(finalParameters),
          status: "PENDING",
        });
        triggerCompressionFunction(createdTask.taskId);

        const message = formatMessage(PCSD_INFO_MESSAGES.IMEC0007, [createdTask.taskName]);
        return NextResponse.json({ message: message }, { status: 200 });
      }
    } else {
      // 「選択ダウンロード」の場合の処理
      const createdTask = await TasksService.createTask({
        schemaName: INSERT_SCHEMA_NAME,
        requestingUserId: INSERT_REQUESTING_USER_ID,
        taskType: TASK_TYPE,
        parameters: JSON.stringify(finalParameters),
        status: "PENDING",
      });
      triggerCompressionFunction(createdTask.taskId);

      const message = formatMessage(PCSD_INFO_MESSAGES.IMEC0007, [createdTask.taskName]);
      return NextResponse.json({ message: message }, { status: 200 });
    }
  } catch (error) {
    console.error("タスクの作成に失敗しました:", error);
    // エラーハンドラで統一したエラー応答を返す
    return handleApiError(error);
  }
}
