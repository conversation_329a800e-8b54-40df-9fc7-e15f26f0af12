import { NextRequest, NextResponse } from "next/server";
import { BlobActions } from "@/app/lib/azure-blob";
import Logger from "@/app/lib/logger";
import { ENV } from "@/app/lib/definitions";
import { fetchTaskForDownload } from "@/app/lib/data/tasks";

/**
 * ファイルダウンロードのGETリクエストを処理するハンドラです。
 * URLパスから受け取ったタスクIDに基づき、対応するZIPファイルの
 * 一時的なダウンロードリンク（SAS URL）を生成し、クライアントをそのURLへリダイレクトさせます。
 * @param {NextRequest} _request - クライアントからのリクエストオブジェクト（この処理では直接は使用しません）。
 * @param {{ params: { taskId: string } }} { params } - URLの動的セグメントから抽出されたパラメータ。
 * @returns {Promise<NextResponse>} - ダウンロードURLへのリダイレクトレスポンス、またはエラーレスポンスを返します。
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: { taskId: string } },
) {
  const { taskId } = params;

  // --- 1. パラメータ検証 ---
  // 必須パラメータであるタスクIDがURLに含まれているかを確認します。
  if (!taskId) {
    Logger.error({
      message: "タスクファイルダウンロード: タスクIDが提供されていません。",
    });
    return new NextResponse(
      "タスクファイルダウンロード: タスクIDが提供されていません。",
      { status: 400 }, // 400 Bad Request
    );
  }

  try {
    // --- 2. データ取得と検証 ---
    // データアクセス層を呼び出し、タスクIDを基にデータベースからタスク情報を取得・検証します。
    const task = await fetchTaskForDownload(taskId);

    // タスクが存在しない、またはステータスがダウンロードに適していない場合の処理。
    if (!task) {
      Logger.error({
        message:
          "タスクファイルダウンロード: 対象タスクが見つからない、またはステータスが不正",
        taskId,
      });
      return new NextResponse("Task not found or not completed", {
        status: 404, // 404 Not Found
      });
    }

    // --- 3. SAS URLの生成 ---
    // 検証済みタスクからAzure Blob Storage内のファイルパス（Blobパス）を取得します。
    const ZIP_BLOB_PATH = task.ZIP_BLOB_PATH;

    // コンテナ名とBlobパスを基に、安全なダウンロード用のSAS URLを生成します。
    const blobUrlWithSAS = await BlobActions.generateBlobUrlWithSAS(
      ENV.AZURE_STORAGE_TEMP_ZIP_CONTAINER_NAME,
      ZIP_BLOB_PATH,
    );

    // --- 4. リダイレクト ---
    // 生成したSAS URLへのリダイレクトレスポンスを返却します。
    // ブラウザはこのレスポンスを受け取ると、自動的にファイルのダウンロードを開始します。
    return NextResponse.redirect(new URL(blobUrlWithSAS));
  } catch (error: any) {
    // --- 5. エラーハンドリング ---
    // DBアクセスやSAS URL生成など、tryブロック内で発生した予期せぬエラーを捕捉します。
    Logger.error({
      message: "タスクファイルダウンロード: 予期せぬエラーが発生",
      taskId,
      errorMessage: error.message,
      errorStack: error.stack,
      errorName: error.name,
    });
    // 汎用的なサーバーエラーメッセージをクライアントに返します。
    return new NextResponse(
      "サーバに一時的に接続できません。しばらくしてから再度ポータルにアクセスしてください。",
      { status: 500 }, // 500 Internal Server Error
    );
  }
}
