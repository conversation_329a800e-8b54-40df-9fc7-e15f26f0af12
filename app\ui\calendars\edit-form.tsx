/**
 * @file edit-form.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */
"use client";

import React, { ChangeEvent, useState } from "react";
import { Overtime } from "@/app/lib/overtime";
import Image from "next/image";
import {
  PC_MODES,
  PCSD_ERROR_MESSAGES,
  PCSD_INFO_MESSAGES,
} from "@/app/lib/definitions";
import PasswordInput from "@/app/ui/passwordinput";
import Tooltip from "@/app/ui/tooltip";
import defaultClient from "@/app/templates/defaultConfig.json";
import { useRouter, useSearchParams } from "next/navigation";
import FeedbackModal from "../feedback-modal";
import ConfirmModal from "@/app/ui/confirm-modal";
import clsx from "clsx";
import useSession from "@/app/hooks/use-session";

const monitorTextInputBoxTooltip = `
PC利用時間モニタ（{0}）に表示されるテキストを指定します。
<br><br>メッセージは2行まで入力できます。1行に80文字まで入力できます。
<br><br>1行は23文字以内を推奨します。
<br><br>23文字を超えるとクライアントPCでメッセージが正しく表示されないことがあります。
`;

export default function Form({
  overtime,
  id,
  page,
}: {
  overtime: Overtime;
  id: string;
  page: string;
}) {
  const router = useRouter();

  // 警告設定用の状態管理
  const [isWarningIntervalDisabled, setIsWarningIntervalDisabled] = useState(
    overtime.OvertimeMode !== "WARNING",
  );
  const [isWarningTextDisabled, setIsWarningTextDisabled] = useState(
    overtime.OvertimeMode !== "WARNING",
  );
  // PC利用停止直前状態移行時刻
  const [sdBeforeTime, setBeforeTime] = useState(
    overtime.WarningNotificationTime,
  );
  const [warningNotificationTimeErrMsg, setWarningNotificationTimeErrMsg] =
    useState("");
  const { getSession } = useSession();
  // 検索パラメータをURLエンコードして取得
  const searchParams = useSearchParams();
  const [params, setParams] = useState(new URLSearchParams(searchParams));

  // クライアント設定を格納する
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    // エラー存在有無の検査、任意エラーメッセージが存在場合、処理中止する
    const areAllEmpty = [
      warningNotificationTimeErrMsg,
      mointorTextErrMsg,
      shutdownBeforeTextErrMsg,
      extrnalTimeTextErrMsg,
      secondsOfShutdownErrMsg,
      warningIntervalTimeErrMsg,
      warningTextErrMsg,
      uninstallPwdErrMsg,
    ].every((value) => value === "");

    if (!areAllEmpty) {
      return;
    }

    // Formのすべてのデータをキーペアに自動的に直列化する、FormDataオブジェクトを作成する
    const formData = new FormData(event.currentTarget);
    formData.append("id", id);

    // 警告設定が無効でない場合、警告関連の設定をフォームに追加
    if (shutdownMode !== "WARNING") {
      // warningIntervalTime
      formData.append("warningInterval", warningIntervalTime);
      formData.append("warningText", warningText);
    } else {
      // 特に処理がない
    }

    // FormDataオブジェクトを作成する
    const formObject = Object.fromEntries(formData);

    // クライアント設定APIにPOSTリクエストを送信する
    getSession();
    const res = await fetch("/api/calendars/" + id + "/editsettings", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(formObject),
    });

    if (res.ok) {
      showMessageModal(
        PCSD_ERROR_MESSAGES.EMEC0015.replace(
          "{0}",
          "クライアント動作設定の保存",
        ),
      );
      // サーバーコンポーネントを更新する
      router.refresh();
      // 操作が正常に完了し、ページが遷移する場合、前のページに戻る際に最初のページ（第１ページ）が表示され
      params.set("page", "1");
      setParams(params);
    } else if (res.status === 401) {
      router.push("/login");
      return;
    } else {
      showErrorModal(
        PCSD_ERROR_MESSAGES.EMEC0014.replace(
          "{0}",
          "クライアント動作設定の保存",
        ),
      );
    }
    setIsMaskVisible(false);
    return;
  };

  // テキストエリアのチェック(PC利用時間外表示テキスト、PC利用停止直前表示テキスト、PC利用時間内表示テキスト)
  const validateTextareaInput = (
    textareaName: string,
    value: string,
    maxLines: number,
    maxCharsPerLine: number,
  ): { isValid: boolean; errorMsg: string } => {
    const lines = value.split("\n");

    // 入力の行数がmaxLinesと超える場合
    if (lines.length > maxLines) {
      return {
        isValid: false,
        errorMsg: `${PCSD_ERROR_MESSAGES.EMEC0013.replace("{0}", textareaName).replace("{1}", maxLines.toString()).replace("{2}", maxCharsPerLine.toString())}`,
      };
    } else {
      // 特に処理がない
    }

    // 各行の文字数チェック
    for (const line of lines) {
      if (line.length > maxCharsPerLine) {
        return {
          isValid: false,
          errorMsg: `${PCSD_ERROR_MESSAGES.EMEC0013.replace("{0}", textareaName).replace("{1}", maxLines.toString()).replace("{2}", maxCharsPerLine.toString())}`,
        };
      } else {
        // 特に処理がない
      }
    }

    return {
      isValid: true,
      errorMsg: "",
    };
  };

  // アンインストールパスワードチェック
  const validateUninstallPwd = (
    value: string,
  ): { isValid: boolean; errorMsg: string } => {
    // 10文字超える場合
    if (value && value.length > 10) {
      return {
        isValid: false,
        errorMsg: `${PCSD_ERROR_MESSAGES.EMEC0005.replace("{0}", "10文字以内の半角英数字")}`,
      };
    } else {
      // 特に処理がない
    }
    const regex = /^[a-zA-Z0-9]+$/;
    // 半角英数字ではない
    if (value && !regex.test(value)) {
      return {
        isValid: false,
        errorMsg: `${PCSD_ERROR_MESSAGES.EMEC0005.replace("{0}", "10文字以内の半角英数字")}`,
      };
    } else {
      // 特に処理がない
    }
    return {
      isValid: true,
      errorMsg: "",
    };
  };

  // PC停止猶予時間チェック
  const validateSecondsOfShutdown = (
    value: number,
  ): { isValid: boolean; errorMsg: string } => {
    if (!/^\d+$/.test(value.toString()) || value > 3600 || value < 30) {
      return {
        isValid: false,
        errorMsg: `${PCSD_ERROR_MESSAGES.EMEC0005.replace("{0}", "30～3600秒")}`,
      };
    } else {
      return {
        isValid: true,
        errorMsg: "",
      };
    }
  };

  // 警告表示間隔時間チェック
  const validateWarningIntervalTime = (
    value: string,
  ): { isValid: boolean; errorMsg: string } => {
    if (value > "06:00" || value < "00:05") {
      return {
        isValid: false,
        errorMsg: `${PCSD_ERROR_MESSAGES.EMEC0005.replace("{0}", "5分～6時間")}`,
      };
    } else {
      // 特に処理がない
    }

    return {
      isValid: true,
      errorMsg: "",
    };
  };

  const handleBeforeTimeChange = (event: ChangeEvent<HTMLInputElement>) => {
    setBeforeTime(event.target.value);

    if (event.target.value.length !== 8) {
      setWarningNotificationTimeErrMsg(
        "PC利用停止直前状態移行時刻時間を指定してください。",
      );
    } else {
      setWarningNotificationTimeErrMsg("");
    }
  };

  // PC利用時間内表示テキスト
  const [mointorText, setMointorText] = useState(
    overtime.MonitorTextNormal || "",
  );
  const [mointorTextErrMsg, setMointorTextErrMsg] = useState("");
  const handleMointorTextChange = (event: any) => {
    setMointorText(event.target.value);
    // 入力チェック
    const { isValid, errorMsg } = validateTextareaInput(
      "PC利用時間内表示テキスト",
      event.target.value,
      2,
      80,
    );
    setMointorTextErrMsg(errorMsg);
  };

  // PC利用停止直前表示テキスト
  const [shutdownBeforeText, setShutdownBeforeText] = useState(
    overtime.MonitorTextWarning || "",
  );
  const [shutdownBeforeTextErrMsg, setShutdownBeforeTextErrMsg] = useState("");
  const handleShutdownBeforeTextChange = (event: any) => {
    setShutdownBeforeText(event.target.value);
    // 入力チェック
    const { isValid, errorMsg } = validateTextareaInput(
      "PC利用停止直前表示テキスト",
      event.target.value,
      2,
      80,
    );
    setShutdownBeforeTextErrMsg(errorMsg);
  };

  // PC利用時間外表示テキスト
  const [extrnalTimeText, setExtrnalTimeText] = useState(
    overtime.MonitorTextTimeover || "",
  );
  const [extrnalTimeTextErrMsg, setExtrnalTimeTextErrMsg] = useState("");
  const handleExtrnalTimeTextChange = (event: any) => {
    setExtrnalTimeText(event.target.value);
    // 入力チェック
    const { isValid, errorMsg } = validateTextareaInput(
      "PC利用時間外表示テキスト",
      event.target.value,
      2,
      80,
    );
    setExtrnalTimeTextErrMsg(errorMsg);
  };

  // PC停止猶予時間
  const [secondsOfShutdown, setSecondsOfShutdown] = useState(
    overtime.ShutdownExtensionSec || "",
  );
  const [secondsOfShutdownErrMsg, setSecondsOfShutdownErrMsg] = useState("");
  const handleSecondsOfShutdownChange = (event: any) => {
    setSecondsOfShutdown(event.target.value);
    // 入力チェック
    const { isValid, errorMsg } = validateSecondsOfShutdown(event.target.value);
    setSecondsOfShutdownErrMsg(errorMsg);
  };

  // 利用者PC動作モード
  const [shutdownMode, setShutdownMode] = useState(overtime.OvertimeMode || "");
  const handleShutdownModeChange = (event: any) => {
    setShutdownMode(event.target.value);
    if (event.target.value !== "WARNING") {
      setIsWarningIntervalDisabled(true);
      setIsWarningTextDisabled(true);
    } else {
      setIsWarningIntervalDisabled(false);
      setIsWarningTextDisabled(false);
    }
  };

  // 警告表示間隔時間
  const [warningIntervalTime, setIntervalTime] = useState(
    overtime.WarningDispIntervalTime,
  );
  const [warningIntervalTimeErrMsg, setWarningIntervalTimeErrMsg] =
    useState("");
  const handleIntervalChange = (event: any) => {
    setIntervalTime(event.target.value);
    // 入力チェック
    const { isValid, errorMsg } = validateWarningIntervalTime(
      event.target.value,
    );
    setWarningIntervalTimeErrMsg(errorMsg);
  };

  // 警告表示テキスト
  const [warningText, setWarningText] = useState(
    overtime.WarningDispText || "",
  );
  const [warningTextErrMsg, setWarningTextErrMsg] = useState("");
  const handleWarningTextChange = (event: any) => {
    setWarningText(event.target.value);
    // 入力チェック
    const { isValid, errorMsg } = validateTextareaInput(
      "警告表示テキスト",
      event.target.value,
      3,
      80,
    );
    setWarningTextErrMsg(errorMsg);
  };

  // アンインストールパスワード
  const [uninstallPwd, setUninstallPwd] = useState(
    overtime.UninstallPassword || "",
  );
  const [uninstallPwdErrMsg, setUninstallPwdErrMsg] = useState("");
  const handleUninstallPwdChange = (event: any) => {
    setUninstallPwd(event.target.value);
    // 入力チェック
    const { isValid, errorMsg } = validateUninstallPwd(event.target.value);
    setUninstallPwdErrMsg(errorMsg);
  };

  const setDefaultCallback = () => {
    // PC利用停止直前状態移行時刻
    setBeforeTime(defaultClient.WarningNotificationTime);
    // PC利用時間内表示テキスト
    setMointorText(defaultClient.MonitorTextNormal);
    // PC利用停止直前表示テキスト
    setShutdownBeforeText(defaultClient.MonitorTextWarning);
    // PC利用時間外表示テキスト
    setExtrnalTimeText(defaultClient.MonitorTextTimeover);
    // PC停止猶予時間
    setSecondsOfShutdown(defaultClient.ShutdownExtensionSec);
    // 利用者PC動作モード
    setShutdownMode("NOTHING");
    setIsWarningIntervalDisabled(overtime.OvertimeMode !== "WARNING");
    setIsWarningTextDisabled(overtime.OvertimeMode !== "WARNING");
    // 警告表示間隔時間
    setIntervalTime(defaultClient.WarningDispIntervalTime);
    // 警告表示テキスト
    setWarningText(defaultClient.WarningDispText);
    // アンインストールパスワード
    setUninstallPwd("");
    // 既存エラーメッセージクリア
    clearAllErrors();
    // 利用者PC動作モード変更
    handleShutdownModeChange({
      target: {
        value: "NOTHING",
      },
    });
  };

  // デフォルト設定
  const setDefault = () => {
    setConfirmMsg(`${PCSD_INFO_MESSAGES.IMEC0002}`);
    setCallbackFunc(() => setDefaultCallback);
    setIsConfirmModalVisible(true);
  };

  // 戻るボタンの処理
  const goBackCallback = () => {
    params.delete("r");
    router.push(`/dashboard/calendars?${params.toString()}`);
  };

  // 戻るボタンのクリックイベント
  const goBack = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();

    setConfirmMsg(`${PCSD_INFO_MESSAGES.IMEC0003}`);
    setCallbackFunc(() => goBackCallback);
    setIsConfirmModalVisible(true);
  };

  // 文字列のスペースを生成する関数
  function generateSpaces(count: number): string {
    return "&nbsp;".repeat(count);
  }

  // 警告間隔時間を選択するための時間ピッカー表示
  const handleWarningIntervalClick = () => {
    if (shutdownMode !== "WARNING") return;
    const timeInput = document.getElementById(
      "warningInterval",
    ) as HTMLInputElement;
    if (timeInput && timeInput.showPicker) {
      timeInput.showPicker();
    } else {
      // 特に処理がない
    }
  };

  // PC停止前の時間を選択するための時間ピッカー表示
  const handleShutdownBeforeTimeClick = () => {
    const timeInput = document.getElementById(
      "shutdownBeforeTime",
    ) as HTMLInputElement;
    if (timeInput && timeInput.showPicker) {
      timeInput.showPicker();
    } else {
      // 特に処理がない
    }
  };

  const [feedbackMsg, setFeedbackMsg] = useState<string | null>(null);
  const [feedbackError, setFeedbackError] = useState<string | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isMaskVisible, setIsMaskVisible] = useState(false);

  // モーダルを開きメッセージを設定
  const showMessageModal = (newMessage: string) => {
    setFeedbackMsg(newMessage);
    setFeedbackError(null); // エラークリア
    setIsModalVisible(true); // モーダルを開く
  };
  // モーダルを開きエラーを設定
  const showErrorModal = (newError: string) => {
    setFeedbackError(newError);
    setFeedbackMsg(null); // メッセージクリア
    setIsModalVisible(true); // モーダルを開く
  };

  // モーダルを閉じる
  const closeModal = () => {
    setIsModalVisible(false);
    setFeedbackError(null); // エラークリア
    setFeedbackMsg(null); // メッセージクリア
  };

  const [isConfirmModalVisible, setIsConfirmModalVisible] = useState(false);
  const [confirmMsg, setConfirmMsg] = useState<string | null>(null);
  const [callbackFunc, setCallbackFunc] = useState<() => void>(() => {});

  // 確認モーダルのアクション処理
  const handleConfirmModalAction = () => {
    setIsConfirmModalVisible(false);
    setIsMaskVisible(true);
    callbackFunc();
    setIsMaskVisible(false);
  };

  // 確認モーダルを閉じる
  const closeConfirmModal = () => {
    setIsConfirmModalVisible(false);
  };

  // すべてのエラーメッセージをクリア
  const clearAllErrors = async () => {
    setMointorTextErrMsg("");
    setShutdownBeforeTextErrMsg("");
    setExtrnalTimeTextErrMsg("");
    setSecondsOfShutdownErrMsg("");
    setWarningIntervalTimeErrMsg("");
    setWarningTextErrMsg("");
    setUninstallPwdErrMsg("");
    setWarningNotificationTimeErrMsg("");
  };

  return (
    <form onSubmit={handleSubmit}>
      {isConfirmModalVisible && (
        <ConfirmModal
          message={confirmMsg}
          onAction={handleConfirmModalAction}
          onClose={closeConfirmModal}
        />
      )}
      {isModalVisible && (
        <FeedbackModal
          message={feedbackMsg}
          error={feedbackError}
          onClose={closeModal}
        />
      )}
      {isMaskVisible && (
        <div className="fixed inset-0 bg-gray-900 opacity-50 z-40"></div>
      )}

      <div className="px-4 py-2 text-base justify-between flex">
        <div className="flex items-center">
          <button
            type="submit"
            className="w-28 ms-3 rounded bg-gradient-blue px-3 py-2 text-center text-xs font-medium text-transparent 
            drop-shadow-blue shadow-inner hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
            style={{ backgroundImage: "url(/icons/btn_save.ico)" }}
          >
            保存
          </button>

          <button
            type="button"
            onClick={goBack}
            className="w-28 ms-3 rounded bg-gradient-blue px-3 py-2 text-center text-xs font-medium text-transparent 
            drop-shadow-blue shadow-inner hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
            style={{ backgroundImage: "url(/icons/btn_return.ico)" }}
          >
            戻る
          </button>
        </div>
        <div className="flex flex-row-reverse items-center">
          <button
            type="button"
            onClick={setDefault}
            className="w-35 ms-3 rounded bg-gradient-blue px-3 py-2 text-center text-xs font-medium text-transparent 
            drop-shadow-blue shadow-inner hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
            style={{ backgroundImage: "url(/icons/btn_default.ico)" }}
          >
            デフォルトに戻す
          </button>
        </div>
      </div>

      <div className="overflow-y-auto">
        <div className="px-8 pt-4 text-base">
          <div className="text-sm font-medium">PC利用時間モニタ設定</div>
        </div>

        <div className="px-[50px] space-y-4 bg-white text-base font-medium">
          <div className="flex flex-col gap-4">
            <div className="flex flex-wrap items-center">
              <div className="text-sm w-60 flex items-center">
                <label
                  htmlFor="groupname"
                  className="block text-sm font-medium text-gray-900 pr-1"
                >
                  PC利用停止直前状態移行時刻
                </label>
                <Tooltip
                  text={`
PC利用停止直前状態に移行する時刻を指定します。
<br><br>PC停止時刻からどれくらい前にPC利用停止直前状態へ移行させるかをHH:MM:SS形式で指定します。
<br><br>00:00:00～23:59:59の範囲で指定します。デフォルト値は30分前(00:30:00)です。
                  `}
                  bigTip="big"
                >
                  <Image src="/icons/help.ico" width={23} height={23} alt="" />
                </Tooltip>
              </div>
              <div style={{ position: "relative", width: "130px" }}>
                <input
                  className={clsx(
                    "rounded border border-gray-300 bg-gray-50 p-2 text-sm text-gray-900 focus:border-gray-300 focus:ring-gray-300",
                    {
                      "border-red-500": warningNotificationTimeErrMsg,
                    },
                  )}
                  id="shutdownBeforeTime"
                  name="shutdownBeforeTime"
                  aria-label="Date and time"
                  type="time"
                  step="1"
                  value={sdBeforeTime}
                  onChange={handleBeforeTimeChange}
                />
                <Image
                  src="/icons/clock.png"
                  width={20}
                  height={20}
                  alt=""
                  style={{
                    position: "absolute",
                    right: "18%",
                    top: "50%",
                    transform: "translateY(-50%)",
                    cursor: "pointer",
                    userSelect: "none",
                  }}
                  onClick={handleShutdownBeforeTimeClick}
                />
              </div>
              <p className="text-sm">HH:MM:SS</p>
              <p className="px-10 text-sm">（指定可能：00:00:00～23:59:59）</p>
              <div className="px-0 text-red-500 flex-shrink-0">
                {warningNotificationTimeErrMsg}
              </div>
            </div>

            <div className="flex items-center">
              <div className="text-sm w-60 flex items-center">
                <label
                  htmlFor="groupname"
                  className="block text-sm font-medium text-gray-900 pr-1"
                >
                  PC利用時間内表示テキスト
                </label>
                <Tooltip
                  text={monitorTextInputBoxTooltip.replace(
                    "{0}",
                    "PC利用時間内",
                  )}
                  bigTip="big"
                >
                  <Image src="/icons/help.ico" width={23} height={23} alt="" />
                </Tooltip>
              </div>
            </div>
            <div className="inline-flex flex-wrap items-center -mt-2">
              <textarea
                className={clsx(
                  "w-[32rem] mr-2 rounded bg-gray-50 border-gray-300 border resize-none focus:border-gray-300 focus:ring-gray-300",
                  {
                    "border-red-500": mointorTextErrMsg,
                  },
                )}
                id="mointorText"
                name="mointorText"
                rows={2}
                value={mointorText}
                onChange={handleMointorTextChange}
              />
              <div className="text-red-500 flex-shrink-0">
                {mointorTextErrMsg}
              </div>
            </div>

            <div className="flex items-center">
              <div className="text-sm w-60 flex items-center">
                <label
                  htmlFor="groupname"
                  className="block text-sm font-medium text-gray-900 pr-1"
                >
                  PC利用停止直前表示テキスト
                </label>
                <Tooltip
                  text={monitorTextInputBoxTooltip.replace(
                    "{0}",
                    "PC利用停止直前",
                  )}
                  bigTip="big"
                >
                  <Image src="/icons/help.ico" width={23} height={23} alt="" />
                </Tooltip>
              </div>
            </div>
            <div className="inline-flex flex-wrap items-center -mt-2">
              <textarea
                className={clsx(
                  "w-[32rem] mr-2 rounded bg-gray-50 border-gray-300 border resize-none focus:border-gray-300 focus:ring-gray-300",
                  {
                    "border-red-500": shutdownBeforeTextErrMsg,
                  },
                )}
                id="shutdownBeforeText"
                name="shutdownBeforeText"
                rows={2}
                value={shutdownBeforeText}
                onChange={handleShutdownBeforeTextChange}
              />
              <div className="text-red-500 flex-shrink-0">
                {shutdownBeforeTextErrMsg}
              </div>
            </div>

            <div className="flex items-center">
              <div className="text-sm w-60 flex items-center">
                <label
                  htmlFor="groupname"
                  className="block text-sm font-medium text-gray-900 pr-1"
                >
                  PC利用時間外表示テキスト
                </label>
                <Tooltip
                  text={monitorTextInputBoxTooltip.replace(
                    "{0}",
                    "PC利用時間外",
                  )}
                  bigTip="big"
                >
                  <Image src="/icons/help.ico" width={23} height={23} alt="" />
                </Tooltip>
              </div>
            </div>
            <div className="inline-flex flex-wrap items-center -mt-2">
              <textarea
                className={clsx(
                  "w-[32rem] mr-2 rounded bg-gray-50 border-gray-300 border resize-none focus:border-gray-300 focus:ring-gray-300",
                  {
                    "border-red-500": extrnalTimeTextErrMsg,
                  },
                )}
                id="extrnalTimeText"
                name="extrnalTimeText"
                rows={2}
                value={extrnalTimeText}
                onChange={handleExtrnalTimeTextChange}
              />
              <div className="text-red-500 flex-shrink-0">
                {extrnalTimeTextErrMsg}
              </div>
            </div>
          </div>
        </div>

        <div className="px-8 py-4 text-base">
          <div className="text-sm font-medium flex items-center flex-wrap">
            <div className="w-60 flex items-center">
              <label
                htmlFor="groupname"
                className="block text-sm font-medium text-gray-900 pr-1"
              >
                PC停止猶予時間
              </label>
              <Tooltip
                text={`
PC停止時刻に到達後，実際にPCを停止するまでの猶予時間を秒単位で指定します。
<br><br>30～3600秒の範囲で指定します。デフォルト値は300秒です。
                  `}
                bigTip="big"
              >
                <Image src="/icons/help.ico" width={23} height={23} alt="" />
              </Tooltip>
            </div>
            <input
              className={`rounded ml-4 border border-gray-300 bg-gray-50 p-2 text-sm text-gray-900 focus:border-gray-300 focus:ring-gray-300 ${secondsOfShutdownErrMsg ? "border-red-500" : ""} `}
              aria-label="Date and time"
              id="secondsOfShutdown"
              name="secondsOfShutdown"
              type="number"
              value={secondsOfShutdown}
              onChange={handleSecondsOfShutdownChange}
              autoComplete="off"
            />
            <span className="ml-2">秒</span>
            <p className="px-10">(指定可能：30～3600秒)</p>
            <div className="text-red-500 text-base flex-shrink-0">
              {secondsOfShutdownErrMsg}
            </div>
          </div>
        </div>

        <div className="px-8 py-4 text-base">
          <div className="text-sm font-medium flex items-center">
            <div className="w-60 flex items-center">
              <label
                htmlFor="groupname"
                className="block text-sm font-medium text-gray-900 pr-1"
              >
                利用者PC動作モード
              </label>
              <Tooltip
                text={`
シャットダウン${generateSpaces(9)}PC 利用時間外に，シャットダウンを行います。
<br>${generateSpaces(30)}利用者環境がリモートデスクトップユーザである場
<br>${generateSpaces(30)}合、ログオフとして動作します。
<br><br>強制シャットダウン${generateSpaces(3)}PC 利用時間外に，シャットダウンを行います。
<br>${generateSpaces(30)}シャットダウンを妨げるアプリケーションが起動中
<br>${generateSpaces(30)}の場合でも，強制的にシャットダウンを行います。
<br>${generateSpaces(30)}利用者環境がリモートデスクトップユーザである場
<br>${generateSpaces(30)}合、強制ログオフとして動作します。
<br><br>ログオフ${generateSpaces(18)}PC 利用時間外に，ログオフを行います。
<br><br>強制ログオフ${generateSpaces(12)}PC 利用時間外に，ログオフを行います。
<br>${generateSpaces(30)}ログオフを妨げるアプリケーションが起動中の場合
<br>${generateSpaces(30)}でも，強制的にログオフを行います。
<br><br>ロック画面表示${generateSpaces(9)}PC 利用時間外に，画面ロックを行います。
<br><br>警告表示${generateSpaces(18)}PC 利用時間外に，定期的に警告表示画面をポップア
<br>${generateSpaces(30)}ップ表示します。表示するメッセージは設定によっ
<br>${generateSpaces(30)}て変更することができます。
<br><br>何もしない${generateSpaces(15)}PC 利用時間外になってもなにも行いません。本シス
<br>${generateSpaces(30)}テムを一時的に無効化したいときに設定します。
              `}
                torb="top"
                bigTip="big"
              >
                <Image src="/icons/help.ico" width={23} height={23} alt="" />
              </Tooltip>
            </div>
            <select
              id="shutdownMode"
              name="shutdownMode"
              className="w-64 ml-4 block cursor-pointer rounded-md bg-gray-50 border border-gray-200 py-2 pl-3 text-sm outline-2 placeholder:text-gray-500 focus:border-gray-300 focus:ring-gray-300"
              value={shutdownMode}
              onChange={handleShutdownModeChange}
            >
              {PC_MODES.map((mode) => (
                <option key={mode.key} value={mode.key}>
                  {mode.value}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="px-[50px] space-y-4 bg-white text-base">
          <div className="flex flex-col gap-4">
            <div className="flex items-center flex-wrap">
              <div className="text-sm w-60 flex items-center">
                <label
                  htmlFor="groupname"
                  className="block text-sm font-medium text-gray-900  pr-1"
                >
                  警告表示間隔時間
                </label>
                <Tooltip
                  text={`
警告表示画面をポップアップする間隔をHH:MM形式で指定します。
<br><br>00:05～06:00の範囲で指定します。デフォルト値は30分間隔(00:30)です。
                  `}
                  bigTip="big"
                  visible={!isWarningIntervalDisabled}
                >
                  <Image src="/icons/help.ico" width={23} height={23} alt="" />
                </Tooltip>
              </div>
              <div style={{ position: "relative", width: "110px" }}>
                <input
                  className={clsx(
                    "rounded border border-gray-300 p-2 text-sm focus:border-gray-300 focus:ring-gray-300",
                    {
                      "border-red-500": warningIntervalTimeErrMsg,
                      "bg-gray-200 text-gray-400": isWarningIntervalDisabled,
                      "bg-gray-50 text-gray-900": !isWarningIntervalDisabled,
                    },
                  )}
                  id="warningInterval"
                  name="warningInterval"
                  aria-label="Date and time"
                  type="time"
                  // step="1"
                  value={warningIntervalTime}
                  disabled={isWarningIntervalDisabled}
                  onChange={handleIntervalChange}
                />
                <Image
                  src="/icons/clock.png"
                  width={20}
                  height={20}
                  alt=""
                  style={{
                    position: "absolute",
                    right: "25%",
                    top: "50%",
                    transform: "translateY(-50%)",
                    cursor: "pointer",
                    userSelect: "none",
                  }}
                  onClick={handleWarningIntervalClick}
                />
              </div>
              <p className="text-sm">HH:MM</p>
              <p className="px-10 text-sm">(指定可能：5分～6時間)</p>
              <div className="text-red-500 flex-shrink-0">
                {warningIntervalTimeErrMsg}
              </div>
            </div>

            <div className="flex items-center">
              <div className="text-sm w-60 flex items-center">
                <label
                  htmlFor="groupname"
                  className="block text-sm font-medium text-gray-900 pr-1"
                >
                  警告表示テキスト
                </label>
                <Tooltip
                  text={`
警告表示画面に表示されるテキストを指定します。
<br><br>メッセージは3行まで入力できます。1行に80文字まで入力できます。
<br><br>1行は19文字以内を推奨します。
<br><br>19文字を超えるとクライアントPCでメッセージが正しく表示されないことがあります。
                  `}
                  torb="top"
                  bigTip="big"
                  visible={!isWarningIntervalDisabled}
                >
                  <Image src="/icons/help.ico" width={23} height={23} alt="" />
                </Tooltip>
              </div>
            </div>
            <div className="inline-flex flex-wrap items-center -mt-2">
              <textarea
                className={clsx(
                  "w-full mr-2 rounded border-gray-300 border resize-none focus:border-gray-300 focus:ring-gray-300",
                  {
                    "border-red-500": warningTextErrMsg,
                    "bg-gray-200 text-gray-400": isWarningTextDisabled,
                    "bg-gray-50 text-gray-900": !isWarningTextDisabled,
                  },
                )}
                style={{ width: "512px" }}
                id="warningText"
                name="warningText"
                rows={3}
                value={warningText}
                disabled={isWarningTextDisabled}
                onChange={handleWarningTextChange}
              />
              <div className="text-red-500 flex-shrink-0">
                {warningTextErrMsg}
              </div>
            </div>
          </div>
        </div>

        <div className="px-8 py-4 text-base">
          <div className="text-sm font-medium flex flex-wrap items-center">
            <div className="w-60 flex items-center">
              <label
                htmlFor="groupname"
                className="block text-sm font-medium text-gray-900 pr-1"
              >
                アンインストールパスワード
              </label>
              <Tooltip
                text={`
アンインストールのパスワード保護機能を有効にする場合はパスワードを指定します。
<br><br>10バイト以内の半角英数字で指定します。
<br><br>未設定の場合は，アンインストール時にパスワードを要求されません。
                  `}
                torb="top"
                bigTip="big"
              >
                <Image src="/icons/help.ico" width={23} height={23} alt="" />
              </Tooltip>
            </div>
            <PasswordInput
              id="uninstallPwd"
              value={uninstallPwd}
              onChange={handleUninstallPwdChange}
              isError={uninstallPwdErrMsg ? true : false}
            />
            <div className="text-base text-red-500 flex-shrink-0">
              {uninstallPwdErrMsg}
            </div>
          </div>
        </div>
      </div>
    </form>
  );
}
