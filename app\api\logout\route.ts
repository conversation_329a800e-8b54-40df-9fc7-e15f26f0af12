/**
 * @file route.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import Logger from "@/app/lib/logger";
import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";
import { ENV, PCSD_ERROR_MESSAGES } from "../../lib/definitions";

// POSTメソッドの実装
export async function POST() {
  // セッションオブジェクトの初期化
  const session = await getIronSession<SessionData>(cookies(), sessionOptions);
  // KEYCLOAKのREALM
  const realm = ENV.KEYCLOAK_REALM;
  // KEYCLOAKのCLIENT
  const clientId = ENV.KEYCLOAK_CLIENT;
  // KEYCLOAKサービスドメイン
  const domainName = ENV.KEYCLOAK_INTERNAL_DOMAIN_NAME;
  // KEYCLOAKサーバのクライアント認証器
  const clientSecret = ENV.KEYCLOAK_CLIENT_SECRET;
  // プロファイル内容取得検証
  if (!realm || !clientId || !domainName || !clientSecret) {
    return NextResponse.json(
      { error: PCSD_ERROR_MESSAGES.EMEC0018 },
      { status: 500 },
    );
  }

  try {
    // APIアクセスパラメータ
    const params = new URLSearchParams();
    // 権限タイプ
    params.append('grant_type', 'refresh_token');
    // KEYCLOAKのトークンのリフレッシュ
    params.append('refresh_token', session.user.refreshToken);
    // KEYCLOAKのCLIENT
    params.append('client_id', clientId);
    // Keycloakサーバのクライアント認証器
    params.append('client_secret', clientSecret);
    // APIアクセスアドレス
    const url = `${domainName}/realms/${realm}/protocol/openid-connect/token`;

    // オブジェクトheadersを作成する
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded'
    };
    // APIアクセス
    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: params.toString(),
    });

    if (response.ok) {
      // 取得結果json形式変換
      const data = await response.json();

      const url = `${domainName}/admin/realms/${realm}/users/${session.user.id}/logout`;
      // オブジェクトheadersを作成する
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${data.access_token}`
      };

      // APIアクセス
      const logoutResponse = await fetch(url, {
        method: 'POST',
        headers: headers,
        body: params.toString(),
      });

      // APIの実行に失敗しました
      if (!logoutResponse.ok) {
        // 取得結果json形式変換
        const errorData = await logoutResponse.json();
        Logger.error({ source: domainName, message: errorData });
      }
    }

    // 204を返却
    return new NextResponse(null, { status: 204 });
  } catch (error) {
    // エラーログを出力
    Logger.error({ message: (error as Error).message, stack: (error as Error).stack });
    // 204を返却
    return new NextResponse(null, { status: 204 });
  } finally {
    // セッションの破棄
    session.destroy();
  }
}
