/**
 * @fileoverview Azure Blob Storage関連サーバーアクションモジュール
 * @description
 * <AUTHOR>
 * @copyright Copyright © 2025 Hitachi Solutions, Ltd.
 */

import { BlobSASPermissions, BlobServiceClient } from "@azure/storage-blob";
import { ENV, AZURE_STORAGE_SAS_TTL_SECONDS } from "./definitions";
import Logger, { LogFunctionSignature } from "@/app/lib/logger";

/**
 * BlobServiceClient（マネージドID認証）と、接続文字列用のレガシーSingletonを管理するクラス
 *
 * 通常はDefaultAzureCredentialを使用してセキュアな認証を行うが、
 * SAS生成など一部レガシー用途のみ接続文字列を許可する。
 * アプリケーション全体でBlobServiceClientインスタンスを効率的に管理し、
 * リソース消費を最小限に抑える。
 */
class BlobServiceClientSingleton {
  private static instance: BlobServiceClient | null = null;
  private static legacyInstance: BlobServiceClient | null = null;

  /**
   * 接続文字列認証のBlobServiceClientインスタンス（レガシー用途）を取得する。
   */
  static getLegacyInstance(): BlobServiceClient {
    if (!this.legacyInstance) {
      if (!ENV.AZURE_STORAGE_CONNECTION_STRING) {
        Logger.error("Azure Storageの接続文字列が見つかりません。");
        throw new Error(
          "ファイルのアップロードに失敗しました。時間をおいてから再度実行してください。",
        );
      }
      this.legacyInstance = BlobServiceClient.fromConnectionString(
        ENV.AZURE_STORAGE_CONNECTION_STRING,
      );
      Logger.info({
        message:
          "BlobServiceClientレガシー（接続文字列）インスタンスを生成しました。",
      });
    }
    return this.legacyInstance;
  }

  static async close(): Promise<void> {
    if (this.instance) {
      // BlobServiceClientはcloseメソッドを持たないが、将来SDKが対応した場合はここで拡張すること。
      this.instance = null;
      Logger.info({
        message:
          "BlobServiceClientシングルトンインスタンスをクローズしました。",
      });
    }
    if (this.legacyInstance) {
      this.legacyInstance = null;
      Logger.info({
        message: "BlobServiceClientレガシーインスタンスをクローズしました。",
      });
    }
  }
}

/**
 * Azure Blob Storage関連のサーバーアクションを提供するクラス
 */
export class BlobActions {
  /**
   * Azure Blob StorageのSAS付きURLを生成する（レガシー用途のみ）。
   *
   * @param {string} containerName - コンテナ名。
   * @param {string} ZIP_BLOB_PATH - ZIPファイルのBLOBパス。
   * @returns {Promise<string>} SAS付きBlob URL。
   * @throws {Error} 接続文字列が設定されていない場合にエラーをスローします。
   * @description
   */
  @LogFunctionSignature()
  static async generateBlobUrlWithSAS(
    containerName: string,
    ZIP_BLOB_PATH: string,
  ): Promise<string> {
    if (!ENV.AZURE_STORAGE_CONNECTION_STRING) {
      throw new Error("Azure Storageの接続文字列が見つかりません。");
    }
    // レガシー用途のみ接続文字列のシングルトンを利用
    const blobServiceClient = BlobServiceClientSingleton.getLegacyInstance();
    const containerClient = blobServiceClient.getContainerClient(containerName);
    const blobClient = containerClient.getBlobClient(ZIP_BLOB_PATH);
    const blobUrlWithSAS = await blobClient.generateSasUrl({
      expiresOn: new Date(
        Date.now() + Number(AZURE_STORAGE_SAS_TTL_SECONDS) * 1000,
      ),
      permissions: BlobSASPermissions.parse("r"),
    });
    return blobUrlWithSAS;
  }
}
