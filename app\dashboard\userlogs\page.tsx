/**
 * @file page.tsx
 * @description 利用者環境ログの一覧表示（サーバーコンポーネント版）。データ取得とレイアウトを担当する。
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { fetchUserlogs, fetchUserhavelogs } from "@/app/lib/data/userlogs";
import { VALID_PAGE_SIZES } from "@/app/lib/definitions";
import PageSize from "@/app/ui/page-size";
import Pagination from "@/app/ui/pagination";
import Search from "@/app/ui/search";
import UserlogsClientWrapper from "@/app/ui/userlogs/client-wrapper";
import { TableSkeleton } from "@/app/ui/skeletons";
import { Suspense } from "react";
import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";

interface UserLog {
  USER_ID: string;
  LOG_TIME_YYYYMMDDHHMMSS: string;
}

export default async function Page({
  searchParams,
}: {
  searchParams?: {
    filter?: string;
    page?: string;
    size?: string;
    sort?: string;
    order?: "asc" | "desc";
  };
}) {
  const filter = searchParams?.filter || "";
  const sizeParam = Number(searchParams?.size) || 10;
  const size = VALID_PAGE_SIZES.includes(sizeParam) ? sizeParam : 10;
  const pageParam = Number(searchParams?.page) || 1;
  const sort = searchParams?.sort || "LOG_TIME";
  const order = searchParams?.order || "desc";
  const session = await getIronSession<SessionData>(cookies(), sessionOptions);
  const [userlogsResult, userHaveLogs] = await Promise.all([
    fetchUserlogs(
      session.user.schemaName,
      filter,
      sort,
      order,
      pageParam,
      size,
    ),
    fetchUserhavelogs(session.user.schemaName),
  ]);

  const { items, totalPages } = userlogsResult || { items: [], totalPages: 0 };

  const pageBlobInfos: [string, string[]][] = items.map((item: UserLog) => [
    item.USER_ID,
    [item.LOG_TIME_YYYYMMDDHHMMSS, item.USER_ID],
  ]);
  const allUserIdsForDownload = (userHaveLogs || []).map(
    (user: { USER_ID: string }) => user.USER_ID,
  );

  const currentPage = Math.min(Math.max(pageParam, 1), totalPages);

  return (
    <div className="p-4 h-full flex flex-col space-y-3">
      <Suspense
        key={`${filter}-${currentPage}-${size}-${sort}-${order}`}
        fallback={<TableSkeleton />}
      >
        {/* ラッパーにすべてを渡し、レイアウトをそこで一元管理する */}
        <UserlogsClientWrapper
          // データプロパティ
          blobInfos={pageBlobInfos}
          allUserIdsForDownload={allUserIdsForDownload}
          filter={filter}
          page={currentPage}
          size={size}
          // コンポーネントをプロパティとして渡す
          search={<Search />}
          pagination={
            totalPages > 0 ? (
              <div className="flex items-center">
                <Pagination totalPages={totalPages} />
                <PageSize />
              </div>
            ) : null
          }
        />
      </Suspense>
    </div>
  );
}
