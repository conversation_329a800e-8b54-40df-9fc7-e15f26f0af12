/**
 * @file logout-modal.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

"use client";

import { useRouter } from "next/navigation";
import { ModalProps, PCSD_ERROR_MESSAGES } from "../lib/definitions";
import { useState } from "react";
import Spinner from "./spinner";
import clsx from "clsx";
import Image from "next/image";

// ログアウトダイアログコンポーネント
export default function LogoutModal({ onClose, onError }: ModalProps) {
  const [loading, setLoading] = useState(false); // ローディング状態の管理
  const router = useRouter(); // ルーターのインスタンス取得
  const closeModal = () => {
    onClose?.();
  };
  // ログアウト処理の関数
  const logout = async () => {
    try {
      setLoading(true); // ローディング状態をtrueに設定
      // ログアウト処理
      const logout = await fetch("/api/logout", {
        method: "POST", // POSTリクエスト
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      });

      if (logout.ok) {
        router.push("/login"); // ホームページにリダイレクト
      } else {
        // エラーをキャプチャー処理
        onError?.(PCSD_ERROR_MESSAGES.EMEC0018);
      }
    } catch (error) {
      console.error(error);
      // エラーをキャプチャー処理
      onError?.(PCSD_ERROR_MESSAGES.EMEC0018);
    } finally {
      closeModal();
      setLoading(false); // ローディング状態をfalseに設定
    }
  };
  return (
    <div
      id="logout-modal"
      tabIndex={-1}
      className="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full"
    >
      <div className="relative w-full max-w-md max-h-full">
        <div className="relative rounded shadow bg-gray-600">
          <div className="flex items-center justify-between p-4 border-b rounded-t bg-gradient-header">
            <h3 className="text-lg font-semibold text-white">ログアウト</h3>
            <button
              type="button"
              className="text-gray-400 bg-transparent rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center hover:bg-gray-600 hover:text-white"
              onClick={closeModal}
            >
              <svg
                className="w-3 h-3"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 14 14"
              >
                <path
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                />
              </svg>
              <span className="sr-only">Close modal</span>
            </button>
          </div>
          <div className="h-32 px-8 py-4 space-y-4 bg-white text-base font-medium">
            <div>
              <Image
                src="/dialoginfo_32.png"
                className="w-8 h-8 me-2 inline-block"
                width={32}
                height={32}
                alt="info"
              />
              ログアウトします。よろしいですか？
            </div>
          </div>
          <div className="flex justify-end items-center p-4 border-t rounded-b bg-gradient-header">
            <button
              type="button"
              className="w-28 rounded bg-gradient-dark px-3 py-2 text-center text-xs font-medium text-transparent 
              drop-shadow-dark shadow-dark hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
              onClick={logout}
              style={{ backgroundImage: "url(/icons/btn_ok.ico)" }}
            >
              <Spinner
                className={clsx("inline-block mr-2", { hidden: !loading })}
              />
              OK
            </button>
            <button
              onClick={closeModal}
              type="button"
              className="w-28 ms-3 rounded bg-gradient-blue px-3 py-2 text-center text-xs font-medium text-transparent 
              drop-shadow-blue shadow-inner hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
              style={{ backgroundImage: "url(/icons/btn_cancel.ico)" }}
            >
              戻る
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
