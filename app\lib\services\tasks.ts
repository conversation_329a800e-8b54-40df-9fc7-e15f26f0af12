/**
 * @file tasks.ts
 * @description タスク管理のサービス層 - 共有ビジネスロジック
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import prisma from "@/app/lib/prisma";
import { LogFunctionSignature } from "@/app/lib/logger";
import { handleServerError } from "@/app/lib/portal-error";
import { formatDateToYYYYMMDDHHMMSS } from "@/app/lib/utils";
import { v4 as uuidv4 } from "uuid";

/**
 * タスク作成のパラメータ型定義
 */
export interface CreateTaskParams {
  schemaName: string;
  requestingUserId: string;
  taskType: string;
  parameters: string;
  status: string;
  taskNamePrefix?: string; // オプション：タスク名のプレフィックス（デフォルト: "userwinlog"）
}

/**
 * タスク作成の戻り値型定義
 */
export interface CreateTaskResult {
  taskId: string;
  taskName: string;
}

/**
 * 複数タスク作成のパラメータ型定義
 */
export interface CreateMultipleTasksParams {
  schemaName: string;
  requestingUserId: string;
  taskType: string;
  parametersArray: string[]; // 各タスクのパラメータ配列
  status: string;
  taskNamePrefix?: string;
  totalFileCount: number; // 総ファイル数
  folderDivideBase: number; // 分割基準数
}

/**
 * 複数タスク作成の戻り値型定義
 */
export interface CreateMultipleTasksResult {
  tasks: CreateTaskResult[];
  baseTaskName: string;
}

/**
 * タスク管理のサービスクラス
 * Server ActionとAPI Routeの両方から呼び出される共有ビジネスロジック
 */
export class TasksService {
  /**
   * 新しいタスクをタスク（TASKS）テーブルに1件登録します。
   * @param params タスク作成パラメータ
   * @returns 作成されたタスクのIDとタスク名を含むオブジェクト
   */
  @LogFunctionSignature()
  static async createTask(params: CreateTaskParams): Promise<CreateTaskResult> {
    try {
      const taskId = uuidv4();

      // 統一された作成時間を生成（単一事実来源）
      const createdAt = new Date();
      const timestamp = formatDateToYYYYMMDDHHMMSS(createdAt);

      // タスク名を生成（プレフィックス-タスクタイプ-タイムスタンプ）
      const taskNamePrefix = params.taskNamePrefix || "userwinlog";
      const taskName = `${taskNamePrefix}-${params.taskType}-${timestamp}`;

      const sql = `
        INSERT INTO "TASKS" (
          "TASK_ID", "SCHEMA_NAME", "REQUESTING_USER_ID", "TASK_NAME", "TASK_TYPE", 
          "PARAMETERS", "STATUS", "CREATED_AT"
        ) VALUES (
          $1::uuid, $2, $3, $4, $5, $6, $7, $8::timestamptz
        );
      `;

      await prisma.$queryRawUnsafe(
        sql,
        taskId,
        params.schemaName,
        params.requestingUserId,
        taskName,
        params.taskType,
        params.parameters,
        params.status,
        createdAt,
      );

      return {
        taskId,
        taskName
      };
    } catch (error) {
      handleServerError(error);
      throw error;
    }
  }

  /**
   * 複数のタスクを一括で作成します（トランザクション内で実行）
   * @param params 複数タスク作成パラメータ
   * @returns 作成されたタスクの配列とベースタスク名
   */
  @LogFunctionSignature()
  static async createMultipleTasks(params: CreateMultipleTasksParams): Promise<CreateMultipleTasksResult> {
    try {
      // 統一された作成時間を生成（単一事実来源）
      const createdAt = new Date();
      const timestamp = formatDateToYYYYMMDDHHMMSS(createdAt);

      const taskNamePrefix = params.taskNamePrefix || "userwinlog";
      const baseTaskName = `${taskNamePrefix}-${params.taskType}-${timestamp}`;

      const createdTasks: CreateTaskResult[] = [];

      await prisma.$transaction(async (tx) => {
        for (let i = 0; i < params.parametersArray.length; i++) {
          const taskId = uuidv4();

          // 範囲表示を計算（1ベース）
          const startRange = i * params.folderDivideBase + 1;
          const endRange = Math.min((i + 1) * params.folderDivideBase, params.totalFileCount);
          const rangeString = `(${startRange}-${endRange})`;
          const taskName = `${baseTaskName}-${rangeString}`;

          const sql = `
                        INSERT INTO "TASKS" (
                          "TASK_ID", "SCHEMA_NAME", "REQUESTING_USER_ID", "TASK_NAME", "TASK_TYPE", 
                          "PARAMETERS", "STATUS", "CREATED_AT"
                        ) VALUES (
                          $1::uuid, $2, $3, $4, $5, $6, $7, $8::timestamptz
                        );
                    `;

          await tx.$queryRawUnsafe(
            sql,
            taskId,
            params.schemaName,
            params.requestingUserId,
            taskName,
            params.taskType,
            params.parametersArray[i],
            params.status,
            createdAt,
          );

          createdTasks.push({
            taskId,
            taskName
          });
        }
      });

      return {
        tasks: createdTasks,
        baseTaskName
      };
    } catch (error) {
      handleServerError(error);
      throw error;
    }
  }
}