/**
 * @file api-validator.ts
 * @description API入力パラメータの検証ユーティリティ
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { NextResponse } from 'next/server';
import { PCSD_ERROR_MESSAGES, VALID_PAGE_SIZES } from './definitions';
import { formatMessage } from './utils';

export interface PaginationParams {
  size: string | null;
  page: string | null;
  sort: string | null;
  order: string | null;
}

export interface ValidationRule<T> {
  isValid: (value: T) => boolean;
  errorMessage: string;
}

export class ApiValidator {
  private static readonly DEFAULT_PAGE_SIZE = 10;
  private static readonly DEFAULT_PAGE = 1;

  /**
   * ページネーションパラメータを検証する
   * @param params - 検証するパラメータ
   * @param validSortFields - 有効なソートフィールドの配列
   * @returns エラーレスポンスまたはnull
   */
  static validatePaginationParams(
    params: PaginationParams,
    validSortFields: string[]
  ): NextResponse | null {
    const validations: { [key: string]: ValidationRule<string | null> } = {
      size: {
        isValid: (size) => {
          const num = size ? parseInt(size) : ApiValidator.DEFAULT_PAGE_SIZE;
          return VALID_PAGE_SIZES.includes(num);
        },
        errorMessage: formatMessage(PCSD_ERROR_MESSAGES.EMEC0001, ['ページサイズ']),
      },
      page: {
        isValid: (page) => {
          const num = page ? parseInt(page) : ApiValidator.DEFAULT_PAGE;
          return num > 0;
        },
        errorMessage: formatMessage(PCSD_ERROR_MESSAGES.EMEC0001, ['ページ番号']),
      },
      sort: {
        isValid: (sort) => !sort || validSortFields.includes(sort),
        errorMessage: formatMessage(PCSD_ERROR_MESSAGES.EMEC0001, ['ソートフィールド']),
      },
      order: {
        isValid: (order) => !order || ['asc', 'desc'].includes(order.toLowerCase()),
        errorMessage: formatMessage(PCSD_ERROR_MESSAGES.EMEC0001, ['ソート順']),
      },
    };

    for (const [key, validation] of Object.entries(validations)) {
      if (!validation.isValid(params[key as keyof PaginationParams])) {
        return NextResponse.json(
          { error: validation.errorMessage },
          { status: 400 }
        );
      }
    }

    return null;
  }

  /**
   * パラメータのデフォルト値を設定する
   * @param params - 元のパラメータ
   * @returns デフォルト値が設定されたパラメータ
   */
  static setDefaultPaginationParams(params: PaginationParams): {
    size: number;
    page: number;
    sort: string;
    order: string;
  } {
    return {
      size: Number.parseInt(params.size ?? String(ApiValidator.DEFAULT_PAGE_SIZE)),
      page: Number.parseInt(params.page ?? String(ApiValidator.DEFAULT_PAGE)),
      sort: params.sort ?? '',
      order: params.order?.toLowerCase() ?? 'asc',
    };
  }
}