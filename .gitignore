# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage
/cypress/screenshots
/cypress/videos
/cypress-coverage
/jest-coverage
/reports
.nyc_output
.swc

# next.js
/.next/
/out/

# production
/build
tsconfig.tsbuildinfo

# misc
.DS_Store
*.pem
.vscode/

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env
# .env.local
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

# code counter
.VSCodeCounter
.idea

# ai & docs
.cursor/
.trae/
.kiro/
docs/
