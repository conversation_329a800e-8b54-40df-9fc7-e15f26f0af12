/**
 * @file middleware.ts
 * @description ミドルウェアファイル
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

// 定義パス定数
const PATHS = {
  ROOT: "/",
  LOGIN: "/login",
  DASHBOARD: "/dashboard",
  CALLBACK: "/callback",
  API_PREFIX: "/api/",
  API_CALLBACK: "/api/callback",
  API_LOGOUT: "/api/logout",
  API_LOGIN: "/api/login",
  API_SESSION: "/api/session"
};

export default async function middleware(req: NextRequest) {
  try {
    const path = req.nextUrl.pathname; // リクエストのパスを取得

    const session = await getIronSession<SessionData>(cookies(), sessionOptions); // セッションデータを取得

    // ルートテーブルを使用してパスと対応する処理ロジックを管理
    const routes = [
      { condition: () => !session.user && path === PATHS.ROOT, redirect: PATHS.LOGIN }, // ユーザーがログインしていない場合、ルートパスにアクセスした場合はログインページにリダイレクト
      { condition: () => session.user && [PATHS.ROOT, PATHS.LOGIN].includes(path), redirect: PATHS.DASHBOARD }, // ユーザーがログインしている場合、ルートパスまたはログインページにアクセスした場合はダッシュボードにリダイレクト
      { condition: () => !session.user && path.startsWith(PATHS.DASHBOARD), redirect: PATHS.LOGIN }, // ユーザーがログインしていない場合、ダッシュボードへのアクセスを試みるとログインページにリダイレクト
      { condition: () => !session.user && path === PATHS.CALLBACK, next: true }, // ユーザーがログインしていない場合、コールバックパスにアクセスした場合は次へ進む
      { condition: () => session.user && path === PATHS.CALLBACK, redirect: PATHS.DASHBOARD }, // ユーザーがログインしている場合、コールバックパスにアクセスした場合はダッシュボードにリダイレクト
      { condition: () => !session.user && isUnauthorizedApiPath(path), response: { error: "Unauthorized" }, status: 401 }, // ユーザーがログインしていない場合、認証されていないAPIパスにアクセスした場合は401エラーを返す
      { condition: () => session.user && isAuthorizedApiPath(path), next: true } // ユーザーがログインしている場合、認証されたAPIパスにアクセスした場合は次へ進む
    ];

    for (const route of routes) {
      if (route.condition()) {
        if (route.redirect) {
          return NextResponse.redirect(new URL(route.redirect, req.url)); // リダイレクト先にリダイレクト
        } else if (route.next) {
          return NextResponse.next(); // 次のミドルウェアまたはハンドラーに進む
        } else if (route.response) {
          return NextResponse.json(route.response, { status: route.status }); // 指定されたステータスコードでJSONレスポンスを返す
        }
      }
    }

    return NextResponse.next(); // どの条件にも該当しない場合は次のミドルウェアまたはハンドラーに進む
  } catch (error) {
    console.error("ミドルウェアでのエラー:", error); // エラーをコンソールに出力
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 }); // 500エラーを返す
  }
}

function isUnauthorizedApiPath(path: string): boolean {
  // 認証されていないAPIパスを判定
  return (
    path.startsWith(PATHS.API_PREFIX) &&
    ![
      PATHS.API_CALLBACK,
      PATHS.API_LOGOUT,
      PATHS.API_LOGIN,
      PATHS.API_SESSION
    ].some(prefix => path.startsWith(prefix))
  );
}

function isAuthorizedApiPath(path: string): boolean {
  // 認証されたAPIパスを判定
  return (
    path.startsWith(PATHS.API_PREFIX) &&
    ![
      PATHS.API_CALLBACK,
      PATHS.API_LOGOUT,
      PATHS.API_LOGIN,
      PATHS.API_SESSION
    ].every(prefix => !path.startsWith(prefix))
  );
}