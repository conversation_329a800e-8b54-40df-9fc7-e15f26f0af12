/**
 * @file table.tsx
 * @description タスク一覧を表示するためのテーブルコンポーネントを定義します。
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */
"use client";

import { TableSkeleton } from "@/app/ui/skeletons";
import Image from "next/image";
import { useEffect, useState } from "react";
import FeedbackModal from "../feedback-modal";
import Thead from "../thead";
import Link from "next/link";
import Tooltip from "@/app/ui/tooltip";

/**
 * APIから返されるステータス文字列を、日本語の表示名に変換するためのマップオブジェクト。
 */
const STATUS_MAP: { [key: string]: string } = {
  PENDING: "圧縮待ち",
  COMPRESSING: "圧縮中",
  COMPLETED: "圧縮完了",
  FAILED: "圧縮失敗",
};

/**
 * タスク一覧を表示するテーブルコンポーネントです。
 * 親コンポーネントから渡されるページネーションとソートの情報を基に、表示データを動的に取得します。
 * @param {object} props - コンポーネントのプロパティ。
 * @param {number} props.page - 現在のページ番号。
 * @param {number} props.size - 1ページあたりの表示件数。
 * @param {string} props.sort - ソート対象のカラム名。
 * @param {'asc' | 'desc'} props.order - ソート順。
 */
export default function TasksTable({
  page,
  size,
  sort,
  order,
}: {
  page: number;
  size: number;
  sort: string;
  order: "asc" | "desc";
}) {
  // APIから取得したタスクのリストを管理するstate。
  const [tasklists, settasklists] = useState<any[]>([]);
  // データの読み込み中状態を管理するstate。trueの間はスケルトンUIを表示します。
  const [loading, setLoading] = useState(false);
  // フィードバックモーダルの状態（メッセージ、エラー、表示/非表示）を管理するstate。
  const [feedbackMsg, setFeedbackMsg] = useState<string | null>(null);
  const [feedbackError, setFeedbackError] = useState<string | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);

  // props（ページ、件数、ソート順など）が変更された際に、データを再取得するためのEffectフック。
  useEffect(() => {
    /**
     * サーバーサイドAPIにリクエストを送信し、タスクのリストを取得する非同期関数。
     * @returns {Promise<any>} APIからのレスポンスデータを返します。
     */
    const fetchTaskLists = async () => {
      // データ取得開始前にローディング状態をtrueに設定します。
      setLoading(true);

      // クエリパラメータを構築します。
      const params = new URLSearchParams({
        size: size.toString(),
        page: page.toString(),
        sort: sort,
        order: order,
      });

      // `/api/tasks`エンドポイントにGETリクエストを送信します。
      const res = await fetch(`/api/tasks?${params.toString()}`, {
        method: "GET",
      });

      const data = await res.json();
      return data;
    };

    /**
     * データ取得のプロセス全体（ローディング状態の管理、API呼び出し、結果のstateへの反映）を管理する関数。
     */
    const fetchData = async () => {
      const tasklistsData = await fetchTaskLists();

      // APIレスポンスにエラーが含まれている場合の処理。
      if (tasklistsData.error) {
        showErrorModal(tasklistsData.error); // エラーモーダルを表示。
        settasklists([]); // タスクリストを空にする。
        setLoading(false); // ローディング状態を解除。
        return;
      } else {
        // 正常にデータが取得できた場合、stateを更新します。
        settasklists(tasklistsData);
        setLoading(false); // ローディング状態を解除。
      }
    };

    // 有効なページ番号（1以上）が指定されている場合のみデータ取得を実行します。
    if (page > 0) {
      fetchData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [order, page, size, sort]); // 依存配列：これらの値が変更されるたびにeffectが再実行されます。

  // --- モーダル制御用のヘルパー関数群 ---
  /** 正常系のメッセージモーダルを表示します。 */
  const showMessageModal = (newMessage: string) => {
    setFeedbackMsg(newMessage);
    setFeedbackError(null);
    setIsModalVisible(true);
  };
  /** エラーメッセージモーダルを表示します。 */
  const showErrorModal = (newError: string) => {
    setFeedbackError(newError);
    setFeedbackMsg(null);
    setIsModalVisible(true);
  };
  /** 表示されているモーダルを閉じます。 */
  const closeFeedbackModal = () => {
    setIsModalVisible(false);
    setFeedbackError(null);
    setFeedbackMsg(null);
  };

  // ローディング中は、テーブルの骨組みとなるスケルトンコンポーネントを表示します。
  if (loading) {
    return <TableSkeleton />;
  }

  return (
    <>
      {/* isModalVisibleがtrueの場合、フィードバックモーダルを描画します。 */}
      {isModalVisible && (
        <FeedbackModal
          message={feedbackMsg}
          error={feedbackError}
          onClose={closeFeedbackModal}
        />
      )}
      <div className="overflow-y-auto">
        <table className="whitespace-nowrap w-full h-full text-left text-sm text-gray-500">
          {/* カスタムのテーブルヘッダーコンポーネント。ソート機能などを内包します。 */}
          <Thead
            headers={[
              { key: "no", label: "＃", css: "w-10", isNotSort: true },
              { key: "TASK_NAME", label: "タスク名" },
              { key: "STATUS", label: "ステータス" },
              { key: "STARTED_AT", label: "開始日時" },
              { key: "COMPLETED_AT", label: "終了日時" },
              { key: "REQUESTING_USER_ID", label: "ユーザーID" },
              { key: "download", label: "タスク詳細", isNotSort: true },
            ]}
            defaultOrder="STARTED_AT"
            defaultSort="desc"
          />
          <tbody>
            {/* tasklistsにデータが存在する場合、各タスクを行として描画します。 */}
            {tasklists?.length > 0 ? (
              tasklists.map((tasklist, index) => (
                <tr
                  key={tasklist.ID}
                  className="border-b odd:bg-white even:bg-gray-50"
                >
                  {/* 行番号 */}
                  <td className="px-6 py-3 text-center border-r">
                    {(page - 1) * size + index + 1}
                  </td>

                  {/* タスク名 */}
                  <td className="max-w-120 px-6 py-3 border-r">
                    <Tooltip
                      text={tasklist.TASK_NAME}
                      bigTip="big"
                      style={{ width: "10px", left: "50%" }}
                      torb={
                        index === size - 1 || index === tasklists?.length - 1
                          ? "top"
                          : "bottom"
                      }
                    >
                      <p className="truncate">{tasklist.TASK_NAME}</p>
                    </Tooltip>
                  </td>

                  {/* ステータス */}
                  <td className="max-w-96 px-6 py-3 border-r">
                    <Tooltip
                      text={tasklist.STATUS}
                      bigTip="big"
                      style={{ width: "10px", left: "50%" }}
                      torb={
                        index === size - 1 || index === tasklists?.length - 1
                          ? "top"
                          : "bottom"
                      }
                    >
                      <p className="truncate">
                        {/* STATUS_MAPを使用して、ステータスコードを日本語表示に変換します。 */}
                        {STATUS_MAP[tasklist.STATUS] || tasklist.STATUS}
                      </p>
                    </Tooltip>
                  </td>

                  {/* 開始日時 */}
                  <td className="max-w-96 px-6 py-4 border-r">
                    <Tooltip
                      text={tasklist.STARTED_AT_YYYYMMDDHHMMSS}
                      bigTip="big"
                      style={{ width: "10px", left: "50%" }}
                      torb={
                        index === size - 1 || index === tasklists?.length - 1
                          ? "top"
                          : "bottom"
                      }
                    >
                      <p className="truncate">
                        {tasklist.STARTED_AT_YYYYMMDDHHMMSS}
                      </p>
                    </Tooltip>
                  </td>

                  {/* 終了日時 */}
                  <td className="max-w-96 px-6 py-4 border-r">
                    <Tooltip
                      text={tasklist.COMPLETED_AT_YYYYMMDDHHMMSS}
                      bigTip="big"
                      style={{ width: "10px", left: "50%" }}
                      torb={
                        index === size - 1 || index === tasklists?.length - 1
                          ? "top"
                          : "bottom"
                      }
                    >
                      <p className="truncate">
                        {tasklist.COMPLETED_AT_YYYYMMDDHHMMSS}
                      </p>
                    </Tooltip>
                  </td>

                  {/* ユーザーID */}
                  <td className="px-6 py-4 border-r">
                    <Tooltip
                      text={tasklist.REQUESTING_USER_ID}
                      bigTip="big"
                      style={{ width: "10px", left: "50%" }}
                      torb={
                        index === size - 1 || index === tasklists?.length - 1
                          ? "top"
                          : "bottom"
                      }
                    >
                      <p className="truncate">{tasklist.REQUESTING_USER_ID}</p>
                    </Tooltip>
                  </td>

                  {/* タスク詳細・操作エリア */}
                  <td className="min-w-48 px-6 py-4">
                    <div className="flex items-center gap-3 flex-shrink-0">
                      {/* タスクのステータスが「圧縮完了」の場合のみ、ダウンロード用のリンクアイコンを表示します。 */}
                      {tasklist.STATUS === "COMPLETED" && (
                        <Link
                          href={`/dashboard/tasks/${tasklist.TASK_ID}/download`}
                          prefetch={false}
                          target="_blank" // 新しいタブで開く
                          rel="noopener noreferrer"
                        >
                          <Tooltip
                            text="圧縮ファイルをダウンロード"
                            bigTip="big"
                            style={{ width: "10px", left: "50%" }}
                            torb={
                              index === size - 1 ||
                              index === tasklists?.length - 1
                                ? "top"
                                : "bottom"
                            }
                          >
                            <Image
                              src="/icons/download.ico"
                              className="cursor-pointer w-8 h-8"
                              width={32}
                              height={32}
                              alt="softPack"
                            />
                          </Tooltip>
                        </Link>
                      )}

                      {/* タスクのステータスが「圧縮失敗」の場合、エラーメッセージを表示します。 */}
                      {tasklist.STATUS === "FAILED" && (
                        <span>{tasklist.ERROR_MESSAGE}</span>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td>
                  <div className="p-4"></div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </>
  );
}
