/**
 * @file page.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import ServerData from "@/app/lib/data";
import PageSize from "@/app/ui/page-size";
import Pagination from "@/app/ui/pagination";
import Table from "@/app/ui/calendars/table";
import { TableSkeleton } from "@/app/ui/skeletons";
import { Suspense } from "react";
import Link from "next/link";
import { VALID_PAGE_SIZES } from "@/app/lib/definitions";

export default async function Page({
  searchParams,
}: {
  searchParams?: {
    page?: string;
    size?: string;
    sort?: string;
    order?: "asc" | "desc";
  };
}) {
  // sizeParamを取得し、10、30、50のいずれかであればそのまま使用し、そうでない場合は10を設定
  const sizeParam = Number(searchParams?.size) || 10;
  const size = VALID_PAGE_SIZES.includes(sizeParam) ? sizeParam : 10;

  // pageParamを取得し、無効な場合は1をデフォルト設定
  const pageParam = Number(searchParams?.page) || 1;

  // ソート順を取得し、指定がなければデフォルトで"GROUP_NAME"を設定
  const sort = searchParams?.sort || "GROUP_NAME";

  // ソート順序を取得し、指定がなければデフォルトで"asc"を設定
  const order = searchParams?.order || "asc";

  // カレンダーのページ数を取得し、無効な場合は1をデフォルト設定
  const totalPages = (await ServerData.fetchCalendarsPages(size)) || 1;

  // 現在のページ番号を設定。1未満なら1、totalPagesを超えていればtotalPagesに設定
  const currentPage = Math.min(Math.max(pageParam, 1), totalPages);

  // 検索パラメータをURLエンコードして取得
  const params = new URLSearchParams(searchParams);

  return (
    <div className="p-4 h-full flex flex-col space-y-3">
      <div className="flex-column flex flex-wrap items-center justify-between rounded-t-xl">
        {totalPages > 0 && (
          <div className="flex items-center">
            <Pagination totalPages={totalPages} />
            <PageSize />
          </div>
        )}
        <Link
          href={`./calendars/create?${params.toString()}`}
          className="w-28 ms-3 rounded bg-gradient-blue px-3 py-2 text-center text-xs font-medium text-transparent 
                    drop-shadow-blue shadow-inner hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
          style={{ backgroundImage: "url(/icons/btn_new.ico)" }}
        >
          新規登録
        </Link>
      </div>

      <div className="relative overflow-x-auto overflow-y-auto shadow-md rounded-t-lg">
        <Suspense
          key={currentPage + size + sort + order}
          fallback={<TableSkeleton />}
        >
          <Table page={currentPage} size={size} sort={sort} order={order} />
        </Suspense>
      </div>
    </div>
  );
}
