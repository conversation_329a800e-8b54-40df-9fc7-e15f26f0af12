/**
 * @file global-error.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

"use client";

import { PCSD_ERROR_MESSAGES } from "./lib/definitions";

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <html>
      <body>
        <h2>${PCSD_ERROR_MESSAGES.EMEC0018}</h2>
        <button onClick={() => reset()}>再試行</button>
      </body>
    </html>
  );
}
