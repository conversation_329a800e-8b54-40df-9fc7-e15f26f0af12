/**
 * @file route.ts
 * @description タスク一覧を取得するためのAPIエンドポイントを定義します。
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { NextResponse, type NextRequest } from "next/server";
import { fetchTasksLists } from "@/app/lib/data/tasks";
import { handleApiError } from "@/app/lib/portal-error";
import { ApiValidator, type PaginationParams } from "@/app/lib/api-validator";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";
import { SessionData, sessionOptions } from "@/app/lib/session";

/**
 * ソート可能なフィールド名のホワイトリスト。
 * APIリクエストで指定可能なソート対象カラムをここに制限することで、
 * 意図しないカラムでのソートや、SQLインジェクションのリスクを軽減します。
 * 設計文書に基づき、タスク一覧画面で表示・ソート可能な列のみを含めます。
 */
const VALID_SORT_FIELDS = [
  "TASK_NAME",        // タスク名
  "STATUS",           // ステータス  
  "STARTED_AT",       // 開始日時
  "COMPLETED_AT",     // 終了日時
  "REQUESTING_USER_ID", // ユーザー
];

/**
 * タスク一覧を取得するためのGETリクエストを処理します。
 * クエリパラメータでページネーションとソートのパラメータを受け取ります。
 * @param {NextRequest} request - クライアントからのリクエストオブジェクト。
 * @returns {Promise<NextResponse>} - タスク一覧のデータ、またはエラーレスポンスを返します。
 */
export async function GET(request: NextRequest) {
  try {
    // URLからクエリパラメータを取得します。
    const { searchParams } = new URL(request.url);

    // セッション情報を取得
    const session = await getIronSession<SessionData>(
      cookies(),
      sessionOptions,
    );

    // 対象のディレクトリのパスを構築
    const schemaName = session.user.schemaName;

    // クエリパラメータから受け取ったパラメータを、検証用の`PaginationParams`オブジェクトに集約します。
    const paginationParams: PaginationParams = {
      size: searchParams.get('size') || '10',
      page: searchParams.get('page') || '1',
      sort: searchParams.get('sort') || 'STARTED_AT',
      order: searchParams.get('order') || 'desc',
    };

    // ApiValidatorを使用し、ページネーションとソートに関するパラメータの妥当性を検証します。
    // `VALID_SORT_FIELDS`のリストに含まれないソート指定などはここで弾かれます。
    const validationError = ApiValidator.validatePaginationParams(
      paginationParams,
      VALID_SORT_FIELDS,
    );
    // もし検証でエラーが検出された場合、エラーレスポンスを即座に返却します。
    if (validationError) return validationError;

    // 検証済みパラメータに対し、未指定の場合のデフォルト値設定や型変換を行います。
    // (例: pageが未指定なら1を設定、sizeを文字列から数値に変換するなど)
    const { size, page, sort, order } =
      ApiValidator.setDefaultPaginationParams(paginationParams);

    // データアクセス層のメソッドを呼び出し、データベースからタスク一覧を取得します。
    const data = await fetchTasksLists(
      schemaName,
      size,
      page,
      sort,
      order,
    );

    // 取得したデータをJSON形式でクライアントに返却します。
    return NextResponse.json(data);
  } catch (error) {
    // 予期せぬエラー（JSONパースエラーなど）が発生した場合、
    // 中央集権的なエラーハンドラで処理し、適切なエラーレスポンスを返します。
    return handleApiError(error);
  }
}
