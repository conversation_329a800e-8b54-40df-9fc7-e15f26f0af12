/**
 * @file thead.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

"use client";

import clsx from "clsx";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

interface TheadProps {
  headers: {
    key: string;
    label: string;
    css?: string;
    chk?: boolean;
    isNotSort?: boolean;
    isChkDisable?: boolean;
  }[];
  defaultSort: string;
  defaultOrder: string;
  onSelectAll?: (selected: boolean) => void;
}

// テーブルヘッダーコンポーネント
export default function Thead({
  headers,
  defaultOrder,
  defaultSort,
  onSelectAll,
}: TheadProps) {
  const pathname = usePathname();
  const { replace } = useRouter();
  const searchParams = useSearchParams();
  const [sort, setSort] = useState("");
  const [order, setOrder] = useState("");
  const [allSelected, setAllSelected] = useState(false);

  useEffect(() => {
    setSort(searchParams.get("sort") || defaultOrder);
    setOrder(searchParams.get("order") || defaultSort);
  }, [searchParams]);

  const Icon = ({ isAsc }: { isAsc?: boolean }) => (
    <img
      src="/screenopenicon16.png"
      className={clsx("icon h-4 w-4 transform-gpu", { "rotate-180": isAsc })}
      alt="sort"
    />
  );

  function toogleOrder() {
    const newOrder = order === "asc" ? "desc" : "asc";
    setOrder(newOrder);

    return newOrder;
  }

  function handleSort(sortKey: string) {
    const params = new URLSearchParams(searchParams);

    if (sortKey === sort) {
      params.set("order", toogleOrder());
    } else {
      setSort(sortKey);
      setOrder("asc");
      params.set("sort", sortKey);
      params.set("order", "asc");
    }

    params.set("page", "1");
    replace(`${pathname}?${params.toString()}`);
  }

  const handleSelectAllChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    setAllSelected(isChecked);
    onSelectAll && onSelectAll(isChecked);
  };

  return (
    <thead className="bg-gray-50 text-xs text-gray-700">
      <tr>
        {headers.map((header, index) => (
          <th
            key={header.key}
            scope="col"
            className={clsx("px-6 py-3 ", header.css, {
              "border-l": index !== 0,
            })}
          >
            {header.chk === true ? (
              <div>
                <input
                  type="checkbox"
                  disabled={header.isChkDisable}
                  style={
                    header.isChkDisable
                      ? {
                          backgroundColor: "#ddd",
                          borderColor: "#ccc",
                        }
                      : {}
                  }
                  onChange={handleSelectAllChange}
                />
              </div>
            ) : (
              <>
                {header.isNotSort === true ? (
                  <div className="flex justify-between  hover:opacity-80 transition duration-300">
                    <span>{header.label}</span>
                  </div>
                ) : (
                  <div
                    className="flex justify-between cursor-pointer hover:opacity-80 transition duration-300"
                    onClick={(e) => handleSort(header.key)}
                  >
                    <span>{header.label}</span>
                    {header.key === sort ? (
                      <Icon isAsc={order === "asc"} />
                    ) : (
                      <div className="w-4"></div>
                    )}
                  </div>
                )}
              </>
            )}
          </th>
        ))}
      </tr>
    </thead>
  );
}
