/**
 * @file table.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */
"use client";

import CalendarThead from "../calendar-thead";
import Image from "next/image";
import CalendarsCopyModal from "@/app/ui/calendar-copy-modal";
import { useEffect, useState } from "react";
import Link from "next/link";
import Tooltip from "@/app/ui/tooltip";
import { TableSkeleton } from "@/app/ui/skeletons";
import { useRouter } from "next/navigation";
import FeedbackModal from "../feedback-modal";
import ConfirmModal from "@/app/ui/confirm-modal";
import {
  BASE_CALENDAR_GROUP_NAME,
  PCSD_ERROR_MESSAGES,
  PCSD_INFO_MESSAGES,
} from "@/app/lib/definitions";
import { usePathname, useSearchParams } from "next/navigation";
import { Modal } from "flowbite";
import useSession from "@/app/hooks/use-session";
import { sanitize } from "isomorphic-dompurify";

// ソフトウェアテーブルコンポーネント
export default function CalendarsTable({
  page,
  size,
  sort,
  order,
}: {
  page: number;
  size: number;
  sort: string;
  order: "asc" | "desc";
}) {
  const pathname = usePathname(); // 現在のパスを取得
  const searchParams = useSearchParams(); // 現在のクエリパラメータを取得
  const params = new URLSearchParams(searchParams); // クエリパラメータを取得
  const [refreshId, setRefreshId] = useState(0); // 画面更新用
  const [selectedCalendarId, setSelectedCalendarId] = useState<string>(""); // 選択されたカレンダーのID
  const [selectedCalendarName, setSelectedCalendarName] = useState<string>(""); // 選択されたカレンダーの名前
  const [baseCalendars, setBaseCalendars] = useState<any[]>([]); // 基本カレンダー
  const [calendars, setCalendars] = useState<any[]>([]); // カレンダー
  const [calendarsCopyModal, setCalendarsCopyModal] = useState<Modal>(); // カレンダーコピーモーダル
  const [deletedCalendarId, setdDeletedCalendarId] = useState<string>(""); // 削除されたカレンダーのID
  const [loading, setLoading] = useState(true); // ロード中かどうかを管理するフラグ
  const [actionType, setActionType] = useState<string>(""); // アクションタイプ
  const [actionTypeBeforeConfirm, setActionTypeBeforeConfirm] =
    useState<string>(""); // アクションタイプ
  const [feedbackMsg, setFeedbackMsg] = useState<string>(""); // フィードバックメッセージ
  const [feedbackError, setFeedbackError] = useState<string>(""); // フィードバックエラー
  const [isModalVisible, setIsModalVisible] = useState(false); // モーダルを表示するかどうかを管理するフラグ
  const [isMaskVisible, setIsMaskVisible] = useState(false); // マスクを表示するかどうかを管理するフラグ
  const [confirmMsg, setConfirmMsg] = useState<string>(""); // 確認メッセージ
  const [isConfirmModalVisible, setIsConfirmModalVisible] = useState(false); // 確認モーダルを表示するかどうかを管理するフラグ
  const [isCopyModalVisible, setCopyModalVisible] = useState(false); // コピーモーダルを表示するかどうかを管理するフラグ
  const router = useRouter(); // ルーターを取得
  const { getSession } = useSession();

  // 画面ロード
  useEffect(() => {
    setLoading(true);

    // 基本カレンダー選択
    const fetchBaseCalendars = async () => {
      const responseBaseCal = await fetch("/api/calendars?isBase=1");
      const data = await responseBaseCal.json();
      // setBaseCalendars(data);
      return data;
    };

    // 基本以外カレンダー選択
    const fetchCalendars = async () => {
      const response2 = await fetch(
        `/api/calendars?size=${size}&page=${page}&sort=${sort}&order=${order}`,
      );
      const data = await response2.json();
      // setCalendars(data);
      return data;
    };

    // カレンダー選択
    const fetchData = async () => {
      // カレンダー選択し、結果がない場合、一番画面へ戻る
      const calendarsData = await fetchCalendars();

      if (calendarsData.error) {
        showErrorModal(calendarsData.error);
        setBaseCalendars([]);
        setCalendars([]);
        setLoading(false);
        return;
      }

      if (
        pathname !== "/dashboard/calendars" &&
        (!calendarsData || calendarsData.length === 0)
      ) {
        router.push("/dashboard/calendars");
        return;
      } else {
        // 特に処理がない
      }

      // 基本カレンダー選択
      const baseCalendarsData = await fetchBaseCalendars();
      if (baseCalendarsData.error) {
        showMessageModal(baseCalendarsData.error);
        setBaseCalendars([]);
        setCalendars([]);
        setLoading(false);
        return;
      }

      setBaseCalendars(baseCalendarsData);
      setCalendars(calendarsData);
      setLoading(false);
    };

    fetchData();
  }, [refreshId]);

  useEffect(() => {
    if (!loading) {
      // コピーモーダルを初期化する関数
      const initCalendarModal = () => {
        // モーダルのターゲット要素を取得
        const $targetEl = document.getElementById("calendar-modal");
        // モーダルのオプションを設定
        const options = {
          backdropClasses:
            "modal-backdrop bg-gray-900/50 dark:bg-gray-900/80 fixed inset-0 z-40", // バックドロップのクラス
          closable: false, // モーダルを閉じることができない設定
          onHide: () => {
            setCopyModalVisible(false);
          },
          onShow: () => {
            setCopyModalVisible(true);
          },
        };
        // インスタンスオプションを設定
        const instanceOptions = {
          id: "canlendar-copy-modal", // モーダルのID
          override: true, // 既存のモーダルを上書きする設定
        };

        // const { Modal } = await import("flowbite");
        // モーダルのインスタンスを作成
        const modal = new Modal($targetEl, options, instanceOptions);
        // モーダルのインスタンスを状態に設定
        setCalendarsCopyModal(modal);
      };

      // コピーモーダルを初期化
      initCalendarModal();
    }
  }, [loading]);

  useEffect(() => {
    // 個別設定解除/カレンダー削除
    const fetchCalendars = async () => {
      if (!deletedCalendarId) return;
      setIsMaskVisible(true);
      const response = await fetch(
        "/api/calendars/update?id=" + deletedCalendarId + "&flg=" + actionType,
      );
      const data = await response.json();

      if (response.ok && data.success) {
        showMessageModal(
          `${actionType === "delIniDat" ? PCSD_ERROR_MESSAGES.EMEC0015.replace("{0}", "基本カレンダーに戻すこと") : PCSD_ERROR_MESSAGES.EMEC0015.replace("{0}", "カレンダーの削除")}`,
        );
      } else {
        showErrorModal(
          `${actionType === "delIniDat" ? PCSD_ERROR_MESSAGES.EMEC0014.replace("{0}", "基本カレンダーに戻すこと") : PCSD_ERROR_MESSAGES.EMEC0014.replace("{0}", "カレンダーの削除")}`,
        );
      }
      // setIsMaskVisible(false);
      return data;
    };

    fetchCalendars();
  }, [deletedCalendarId, actionType]);

  // 個別設定解除
  const delIniDat = (calendarId: string) => {
    setSelectedCalendarId(calendarId);
    setActionTypeBeforeConfirm("delIniDat");
    setConfirmMsg(`${PCSD_INFO_MESSAGES.IMEC0004}`);
    setIsConfirmModalVisible(true);
  };

  // カレンダー削除
  const delCalendar = (calendarId: string) => {
    setSelectedCalendarId(calendarId);
    setActionTypeBeforeConfirm("delCalendar");
    setConfirmMsg(`${PCSD_INFO_MESSAGES.IMEC0005}`);
    setIsConfirmModalVisible(true);
  };

  // クライアント動作設定コピー先選択画面を開く
  const openCopyModal = (calendarId: string, calendarGrpName: string) => {
    setSelectedCalendarId(calendarId);
    setSelectedCalendarName(calendarGrpName);

    calendarsCopyModal!.show();
  };

  // クライアント動作設定コピー先選択を閉じる
  const closeModal = (isRefresh: boolean) => {
    calendarsCopyModal!.hide();
    setSelectedCalendarId("");
    setSelectedCalendarName("");
    setIsMaskVisible(false);
    const backdrop = document.querySelector(".modal-backdrop");

    if (backdrop) {
      backdrop.remove();
    }

    if (isRefresh) {
      setRefreshId(refreshId + 1);
    }
  };

  const handleDownload = async (id: any) => {
    getSession();
    setIsMaskVisible(true);
    const response = await fetch("/api/calendars/" + id + "/download", {
      method: "GET",
    });

    if (response.ok) {
      const data = await response.json();
      // Base 64文字列をバイナリデータとして復号
      const binaryData = atob(data.fileData);
      const arrayBuffer = new Uint8Array(binaryData.length);

      for (let i = 0; i < binaryData.length; i++) {
        arrayBuffer[i] = binaryData.charCodeAt(i);
      }
      const blob = new Blob([arrayBuffer], {
        type: "application/octet-stream",
      });
      downloadFile(blob, data.fileName);
    } else {
      if (response.status === 401) {
        router.push("/login");
        return;
      }

      const data = await response.json();

      if (data.success) {
        showMessageModal(data.message);
        setIsMaskVisible(false);
      } else {
        showErrorModal(
          `${PCSD_ERROR_MESSAGES.EMEC0014.replace("{0}", "カレンダー作成ツールのダウンロード")}`,
        );
        setIsMaskVisible(false);
      }
    }
  };

  const downloadFile = (blob: Blob, fileName: string) => {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    a.remove();
    setIsMaskVisible(false);
  };

  // モーダルを開きメッセージを設定
  const showMessageModal = (newMessage: string) => {
    setFeedbackMsg(newMessage);
    setFeedbackError(null); // エラークリア
    setIsModalVisible(true); // モーダルを開く
  };

  // モーダルを開きエラーを設定
  const showErrorModal = (newError: string) => {
    setFeedbackError(newError);
    setFeedbackMsg(null); // メッセージクリア
    setIsModalVisible(true); // モーダルを開く
  };

  // モーダルを閉じる
  const closeFeedbackModal = () => {
    const isFailed = !!feedbackError;

    setIsModalVisible(false);
    setFeedbackError(null); // エラークリア
    setFeedbackMsg(null); // メッセージクリア
    setIsMaskVisible(false);

    // 削除処理が失敗した場合、リフレッシュをしない
    if (isFailed) {
      setdDeletedCalendarId("");
      return;
    }

    if (
      actionType &&
      (actionType === "delIniDat" || actionType === "delCalendar")
    ) {
      params.set("page", "1");
      router.replace(`${pathname}?${params.toString()}`);

      // ページが1の場合、手動でリフレッシュ
      if (page === 1) {
        setRefreshId(refreshId + 1);
      }
    } else {
      router.push("/dashboard/calendars");
    }
    router.refresh(); // ページを更新
  };

  // 確認モーダルのアクション処理
  const handleConfirmModalAction = () => {
    getSession();
    setIsConfirmModalVisible(false);
    setdDeletedCalendarId(selectedCalendarId);
    setActionType(actionTypeBeforeConfirm);
  };

  // 確認モーダルを閉じる
  const closeConfirmModal = () => {
    setIsConfirmModalVisible(false);
  };

  // 個別設定編集押下、個別設定編集画面へ遷移
  const handleEditClick = async (calendarId: string) => {
    params.set("r", Math.random().toString());
    router.push(`./calendars/${calendarId}/edit?${params.toString()}`);
  };

  if (loading) {
    return <TableSkeleton />;
  } else {
    // 特に処理がない
  }

  return (
    <>
      {isConfirmModalVisible && (
        <ConfirmModal
          message={confirmMsg}
          onAction={handleConfirmModalAction}
          onClose={closeConfirmModal}
        />
      )}
      {isModalVisible && (
        <FeedbackModal
          message={feedbackMsg}
          error={feedbackError}
          onClose={() => closeFeedbackModal()}
        />
      )}
      {isMaskVisible && (
        <div className="fixed inset-0 bg-gray-900 opacity-50 z-40"></div>
      )}

      <CalendarsCopyModal
        onClose={closeModal}
        selectedCalendarId={selectedCalendarId ?? ""}
        selectedCalendarName={selectedCalendarName ?? ""}
        isOpen={isCopyModalVisible}
      />
      <div className="overflow-y-auto">
        <table className="whitespace-nowrap w-full h-full text-left text-sm text-gray-500 ">
          <CalendarThead defaultOrder="GROUP_NAME" defaultSort="asc" />
          <tbody>
            {baseCalendars?.length !== 0 ? (
              baseCalendars!.map((baseCalendar) => (
                <tr
                  key={baseCalendar.ID}
                  className="border-b odd:bg-white even:bg-gray-50"
                >
                  <td className="px-6 py-3 border-r">ー</td>
                  <td className="max-w-80 px-6 py-3 border-r">
                    <div className="flex items-center gap-3">
                      <p>{BASE_CALENDAR_GROUP_NAME}</p>
                    </div>
                  </td>
                  <td className="max-w-80 px-6 py-4 border-r">
                    <div className="flex items-center gap-3 justify-between">
                      <div className="flex-grow min-w-16">
                        <Tooltip
                          text={baseCalendar.CALENDAR_TOOL_NAME}
                          torb="bottom"
                        >
                          <p className="truncate">
                            {baseCalendar.CALENDAR_TOOL_NAME}
                          </p>
                        </Tooltip>
                      </div>
                      <div className="flex items-center gap-3 flex-shrink-0">
                        <Tooltip text="アップロード">
                          <Link
                            href={`./calendars/${baseCalendar.ID}/${BASE_CALENDAR_GROUP_NAME}/update?${params.toString()}`}
                          >
                            <Image
                              src="/icons/addFile.ico"
                              className="cursor-pointer w-8 h-8"
                              width={32}
                              height={32}
                              alt="upload"
                            />
                          </Link>
                        </Tooltip>
                        <Tooltip text="ダウンロード">
                          <Image
                            src="/icons/download.ico"
                            className="cursor-pointer w-8 h-8"
                            width={32}
                            height={32}
                            alt="download"
                            onClick={() => handleDownload(baseCalendar.ID)}
                            tabIndex={0}
                          />
                        </Tooltip>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 border-r">
                    {baseCalendar.UPLOAD_DATE_TZ}
                  </td>
                  <td className="min-w-80 px-6 py-3 border-r">
                    <div className="flex items-center gap-3 justify-between">
                      <div className="flex-grow min-w-36">
                        <p>設定済み</p>
                      </div>
                      <div className="flex items-center gap-3 flex-shrink-0">
                        <Tooltip text="個別設定編集">
                          <Image
                            src="/icons/editFile.ico"
                            className="cursor-pointer w-8 h-8"
                            width={32}
                            height={32}
                            alt="edit"
                            tabIndex={0}
                            onClick={() => handleEditClick(baseCalendar.ID)}
                          />
                        </Tooltip>
                        <Image
                          src="/icons/transparent.png"
                          className="opacity-0"
                          width={32}
                          height={32}
                          alt=""
                        />
                        <Image
                          src="/icons/transparent.png"
                          className="opacity-0"
                          width={32}
                          height={32}
                          alt=""
                        />
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 border-r">
                    {baseCalendar.CLIENT_CONFIG_UPDATE
                      ? baseCalendar.CLIENT_CONFIG_UPDATE_TZ
                      : ""}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td>
                  <div className="p-4"></div>
                </td>
              </tr>
            )}
            {calendars?.length !== 0 ? (
              calendars!.map((calendar, index) => (
                <tr
                  key={calendar.ID}
                  className="border-b odd:bg-white even:bg-gray-50"
                >
                  <td className="px-6 py-3 text-center border-r">
                    {(page - 1) * size + index + 1}
                  </td>
                  <td className="max-w-80 px-6 py-3 border-r">
                    <div className="flex items-center gap-3 justify-between">
                      <div className="flex-grow min-w-16">
                        <Tooltip
                          text={calendar.GROUP_NAME}
                          torb={
                            index === size - 1 || index === calendars.length - 1
                              ? "top"
                              : "bottom"
                          }
                          style={{ left: 0, transform: "none" }}
                        >
                          <p className="truncate">
                            {sanitize(calendar.GROUP_NAME)}
                          </p>
                        </Tooltip>
                      </div>
                      <div className="flex items-center flex-shrink-0">
                        <Tooltip
                          text="カレンダー削除"
                          torb={
                            index === size - 1 || index === calendars.length - 1
                              ? "top"
                              : "bottom"
                          }
                        >
                          <Image
                            src="/icons/delFile.ico"
                            className="cursor-pointer w-8 h-8"
                            width={32}
                            height={32}
                            alt="delete"
                            onClick={() => delCalendar(calendar.ID)}
                            tabIndex={0}
                          />
                        </Tooltip>
                      </div>
                    </div>
                  </td>
                  <td className="max-w-80 px-6 py-4 border-r">
                    <div className="flex items-center gap-3 justify-between">
                      <div className="flex-grow min-w-16">
                        <Tooltip
                          text={calendar.CALENDAR_TOOL_NAME}
                          torb={
                            index === size - 1 || index === calendars.length - 1
                              ? "top"
                              : "bottom"
                          }
                        >
                          <p className="truncate">
                            {calendar.CALENDAR_TOOL_NAME}
                          </p>
                        </Tooltip>
                      </div>
                      <div className="flex items-center gap-3 flex-shrink-0">
                        <Tooltip
                          text="アップロード"
                          torb={
                            index === size - 1 || index === calendars.length - 1
                              ? "top"
                              : "bottom"
                          }
                        >
                          <Link
                            className="cursor-pointer"
                            href={`./calendars/${calendar.ID}/${encodeURIComponent(calendar.GROUP_NAME)}/update?${params.toString()}`}
                          >
                            <Image
                              src="/icons/addFile.ico"
                              className="w-8 h-8"
                              width={32}
                              height={32}
                              alt="upload"
                            />
                          </Link>
                        </Tooltip>
                        <Tooltip
                          text="ダウンロード"
                          torb={
                            index === size - 1 || index === calendars.length - 1
                              ? "top"
                              : "bottom"
                          }
                        >
                          <Image
                            src="/icons/download.ico"
                            className="cursor-pointer w-8 h-8"
                            width={32}
                            height={32}
                            alt="download"
                            onClick={() => handleDownload(calendar.ID)}
                            tabIndex={0}
                          />
                        </Tooltip>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 border-r">
                    {calendar.UPLOAD_DATE_TZ}
                  </td>
                  <td className="min-w-80 px-6 py-3 border-r">
                    <div className="flex items-center gap-3 justify-between">
                      <div className="flex-grow min-w-36">
                        <p className="truncate">
                          {calendar.INI_DAT_ISNULL === 1
                            ? "基本カレンダーと同じ"
                            : "個別設定済み"}
                        </p>
                      </div>
                      <div className="flex items-center gap-3 flex-shrink-0">
                        <Tooltip
                          text="個別設定編集"
                          torb={
                            index === size - 1 || index === calendars.length - 1
                              ? "top"
                              : "bottom"
                          }
                        >
                          <Image
                            src="/icons/editFile.ico"
                            className="cursor-pointer w-8 h-8"
                            width={32}
                            height={32}
                            alt="edit"
                            onClick={() => handleEditClick(calendar.ID)}
                            tabIndex={0}
                          />
                        </Tooltip>
                        {calendar.INI_DAT_ISNULL !== 1 ? (
                          <Tooltip
                            text="基本カレンダーに戻す"
                            torb={
                              index === size - 1 ||
                              index === calendars.length - 1
                                ? "top"
                                : "bottom"
                            }
                          >
                            <Image
                              src="/icons/retFile.ico"
                              className="cursor-pointer w-8 h-8"
                              width={32}
                              height={32}
                              alt="Unbind"
                              onClick={() => delIniDat(calendar.ID)}
                              tabIndex={0}
                            />
                          </Tooltip>
                        ) : (
                          <Image
                            src="/icons/transparent.png"
                            className="opacity-0"
                            width={32}
                            height={32}
                            alt=""
                          />
                        )}
                        {calendar.INI_DAT_ISNULL !== 1 ? (
                          <Tooltip
                            text="個別設定コピー"
                            torb={
                              index === size - 1 ||
                              index === calendars.length - 1
                                ? "top"
                                : "bottom"
                            }
                          >
                            <Image
                              src="/icons/copyFile.ico"
                              className="cursor-pointer w-8 h-8"
                              width={32}
                              height={32}
                              alt="copy"
                              onClick={() =>
                                openCopyModal(calendar.ID, calendar.GROUP_NAME)
                              }
                              tabIndex={0}
                            />
                          </Tooltip>
                        ) : (
                          <Image
                            src="/icons/transparent.png"
                            className="opacity-0"
                            width={32}
                            height={32}
                            alt=""
                          />
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 border-r">
                    {calendar.CLIENT_CONFIG_UPDATE
                      ? calendar.CLIENT_CONFIG_UPDATE_TZ
                      : ""}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td>
                  <div className="p-4"></div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </>
  );
}
