/**
 * @file route.ts
 * @description ソフトウェアAPIエンドポイント
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { NextResponse, type NextRequest } from 'next/server';
import ServerData from '@/app/lib/data';
import { handleApiError } from "@/app/lib/portal-error";
import { ApiValidator, type PaginationParams } from '@/app/lib/api-validator';

// 有効なソートフィールドの定義
const VALID_SORT_FIELDS = ['SOFTWARE_NAME', 'VERSION_INFO', 'SOFTWARE_UPDATE_DATE'];

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    // ソフトウェアID
    const softwareId = searchParams.get('id');
    // ソフトウェアのリストを取得する
    const data = await ServerData.fetchSoftwareById(softwareId ?? '');
    return NextResponse.json(data);
  } catch (error) {
    return handleApiError(error);
  }
}

export async function POST(request: NextRequest) {
  try {
    const params = await request.json();

    // ページネーションパラメータを設定
    const paginationParams: PaginationParams = {
      size: String(params.size),
      page: String(params.page),
      sort: params.sort,
      order: params.order,
    };

    // パラメータを検証
    const validationError = ApiValidator.validatePaginationParams(
      paginationParams,
      VALID_SORT_FIELDS
    );
    if (validationError) return validationError;

    // デフォルト値を設定して処理を実行
    const { size, page, sort, order } = ApiValidator.setDefaultPaginationParams(paginationParams);
    const data = await ServerData.fetchSoftwares(size, page, sort, order);

    return NextResponse.json(data);
  } catch (error) {
    return handleApiError(error);
  }
}