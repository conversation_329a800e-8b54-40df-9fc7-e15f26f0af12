/**
 * @file sidebar.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */
"use client";

import clsx from "clsx";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { FC, useEffect } from "react";
import { navLinks2 } from "../lib/definitions";
import { SessionData } from "../lib/session";

interface SidebarProps {
  session: SessionData;
}

interface Menu {
  key: string;
  href: string;
}
// サイドバーコンポーネント
const Sidebar: FC<SidebarProps> = function ({ session }) {
  const pathname = usePathname();

  useEffect(() => {
    if (typeof window !== "undefined") {
      import("flowbite").then(({ initCollapses }) => {
        initCollapses();

        document.querySelectorAll("[data-collapse-toggle]").forEach((item) => {
          item.addEventListener("click", () => {
            const isExpanded = item.getAttribute("aria-expanded") === "true";
            const icon = item.querySelector("img.icon");
            icon && icon.classList.toggle("-rotate-90", !isExpanded);
          });
        });
      });
    } else {
      // 特に処理がない
    }
  }, []);
  const generateHref = (menu: Menu, session: SessionData) => {
    if ("workflow" === menu.key && session) {
      return `https://${session.user.schemaName}-${process.env.NEXT_PUBLIC_WORKFLOW_CONSTANT_LINK}`;
    } else {
      return menu.href;
    }
  };
  return (
    <div className="z-10 w-full h-full overflow-auto p-2 lg:sticky lg:!block">
      <aside
        id="sidebar-multi-level-sidebar"
        className="dark w-full h-full bg-gray-600 bg-gradient-bg shadow-inner drop-shadow"
        aria-label="Sidebar"
      >
        <div className="h-full overflow-y-auto bg-gray-50 px-3 py-4 dark:bg-transparent">
          <ul className="space-y-2 font-medium">
            {navLinks2.map((menu) => (
              <li key={menu.key}>
                <button
                  type="button"
                  className="group flex w-full items-center rounded-lg p-2 text-base text-gray-900 transition duration-75 dark:text-white"
                  aria-controls={menu.key}
                  tabIndex={-1}
                >
                  <Link
                    href={generateHref(menu, session)}
                    target={menu.target}
                    className={clsx(
                      "group flex w-full items-center rounded-lg p-2 pl-5 text-gray-900 transition duration-75 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700",
                      {
                        "from-blue-600 to-blue-500 dark:bg-gradient-to-b":
                          pathname === menu.href,
                      },
                    )}
                  >
                    <p className="ms-3">{menu.name}</p>
                  </Link>
                </button>
              </li>
            ))}
          </ul>
        </div>
      </aside>
    </div>
  );
};
export default Sidebar;
