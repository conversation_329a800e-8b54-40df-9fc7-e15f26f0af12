/**
 * @file utils.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);
/**
 * ページネーションを生成する関数
 * @param currentPage 現在のページ
 * @param totalPages ページ総数
 * @returns 表示文字列配列
 */
export const generatePagination = (currentPage: number, totalPages: number) => {
  // もしページ総数が7以下なら、省略記号なしで全てのページを表示
  if (totalPages <= 7) {
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  }

  // もし現在のページが最初の2ページの中にあれば、最初の3ページ、省略記号、そして最後の2ページを表示
  if (currentPage < 3) {
    return [1, 2, 3, "...", totalPages - 1, totalPages];
  }

  // もし現在のページが最初の3ページの中にあれば、最初の4ページ、省略記号、そして最後の2ページを表示
  if (currentPage == 3) {
    return [1, 2, 3, 4, "...", totalPages - 1, totalPages];
  }

  // もし現在のページが最後の2ページの中にあれば、最初の2ページ、省略記号、そして最後の3ページを表示
  if (currentPage > totalPages - 2) {
    return [1, 2, "...", totalPages - 2, totalPages - 1, totalPages];
  }

  // もし現在のページが最後の3ページの中にあれば、最初の2ページ、省略記号、そして最後の4ページを表示
  if (currentPage == totalPages - 2) {
    return [1, 2, "...", totalPages - 3, totalPages - 2, totalPages - 1, totalPages];
  }

  // もし現在のページが中間にあれば、最初のページ、省略記号、現在のページとその前後のページ、
  // 別の省略記号、そして最後のページを表示
  return [
    1,
    "...",
    currentPage - 1,
    currentPage,
    currentPage + 1,
    "...",
    totalPages,
  ];
};

/**
 * バイト数をフォーマットする関数
 * @param bytes 実際のバイト数
 * @returns 人間が読みやすい文字列
 */
export function formatBytes(bytes: number): string {
  const k = 1024;
  const sizes = ["KB", "MB", "GB"];
  const removeTrailingZeros = (value: string): string => {
    return value.replace(/\.?0+$/, "");
  };

  if (bytes < k * k) {
    return removeTrailingZeros((bytes / k).toFixed(2)) + " " + sizes[0];
  } else if (bytes < k * k * k) {
    return removeTrailingZeros((bytes / (k * k)).toFixed(2)) + " " + sizes[1];
  } else {
    return (
      removeTrailingZeros((bytes / (k * k * k)).toFixed(2)) + " " + sizes[2]
    );
  }
}

/**
 * 時間をフォーマットする関数
 * @param date 実際の時間
 * @param tz 時間帯
 * @returns 「YYYY/MM/DD hh:mm:ss」形式文字列
 */
export function formatDate(date: Date, tz?: string): string {
  if (tz) {
    return dayjs(date).tz(tz).format("YYYY/MM/DD HH:mm:ss");
  } else {
    return dayjs(date).format("YYYY/MM/DD HH:mm:ss");
  }
}

/**
 * データを取得するfetcher関数
 * @param url リモートアドレス
 * @returns 取得したデータ
 */
export const fetcher = async (url: string) => {
  // URLにリクエストを送信し、レスポンスを取得
  const response: Response = await fetch(url);

  // レスポンスのデータをJSON形式で取得
  const data = await response.json();

  // 取得したデータを返す
  return data;
}

/**
 * 指定された日付を「YYYYMMDDhhmmss」形式でフォーマットする関数
 *
 * @param date - 処理対象の日付
 * @returns 「YYYYMMDDhhmmss」形式の文字列
 */
export function formatDateToYYYYMMDDHHMMSS(date: Date) {
  return `${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}${String(date.getDate()).padStart(2, '0')}\
${String(date.getHours()).padStart(2, '0')}${String(date.getMinutes()).padStart(2, '0')}${String(date.getSeconds()).padStart(2, '0')}`;
}

/**
 * 指定されたローカル時間をZIPファイル用のUTC時間に変換
 * ZIPファイル内の時間をローカル時間として表示させるために使用
 *
 * @param lastModified ローカル時間（Dateオブジェクト）
 * @returns ZIPファイル用のUTC時間（Dateオブジェクト）
 */
export function convertToZipUTC(lastModified: Date): Date {
  return new Date(lastModified.getTime() - lastModified.getTimezoneOffset() * 60000);
}

/**
 * 配列内の各サブ配列の特定位置に空文字列があるかチェックする関数
 * 
 * この関数は、UserLogData 配列の各サブ配列の2番目の要素（インデックス1）の最初の項目（インデックス0）が
 * 空文字列であるかどうかをチェックします。もし任意の位置で空文字列でない場合、即座に false を返します。
 * 全てのチェックが通過した場合、true を返します。
 *
 * @param data UserLogData の配列
 * @returns 全ての指定位置が空文字列の場合 true、そうでない場合は false
 */
export const checkForEmptyStringsAtPosition0 = (data: [string, string[]][]): boolean => {
  // トップレベルの配列をループする
  for (let i = 0; i < data.length; i++) {
    // 各サブ配列の2番目の要素（インデックスが1）の最初の項目（インデックスが0）をチェックする
    const firstElementOfSecondItem = data[i][1][0];

    // 空文字列でない場合、すぐに false を返す
    if (firstElementOfSecondItem !== "") {
      return false;
    }
  }

  // 全てのチェックが通過した場合、true を返す
  return true;
};

/**
 * オブジェクトのキーをすべて大文字に変換する
 * この関数は、与えられたオブジェクトのすべてのキーを大文字にして新しいオブジェクトを返します。
 *
 * @param obj 変換するオブジェクト（任意の型）
 * @returns 大文字化されたキーを持つ新しいオブジェクト
 */
export function toUperCaseKeys(obj: any) {

  // 入力が null または undefined の場合、空のオブジェクトを返す
  if (obj === null || obj === undefined) {
    return {};
  }
  // 新しいオブジェクトを作成
  const newObj: any = {};

  // オブジェクトの各キーに対して処理を行う
  for (const key in obj) {
    // キーを大文字に変換
    const uperKey = key.toUpperCase();
    // 新しいオブジェクトに大文字化したキーと対応する値をセット
    newObj[uperKey] = obj[key];
  }
  // 変換後の新しいオブジェクトを返す
  return newObj;
};

/**
 * メッセージをフォーマットする関数
 * 
 * @param template - メッセージテンプレート（例："{0}が正しくありません。"）
 * @param args - 置換する値の配列
 * @returns フォーマットされたメッセージ
 * 
 * @example
 * ```typescript
 * formatMessage("{0}が{1}です。", ["パスワード", "無効"]);
 * // => "パスワードが無効です。"
 * ```
 */
export function formatMessage(template: string, args: string[]): string {
  return template.replace(/\{(\d+)\}/g, (match, index) => {
    return args[parseInt(index)] ?? match;
  });
}