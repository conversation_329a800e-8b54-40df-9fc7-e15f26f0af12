/**
 * @file route.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import iconv from 'iconv-lite';
import ServerData from '@/app/lib/data';
import path from 'path';
import { SJIS_ENCODING } from "@/app/lib/definitions";
import { handleApiError } from "@/app/lib/portal-error";
import { OVERTIME_TEMPLATE, OVERTIME_TEMPLATE_PATH } from '@/app/lib/definitions'
import Logger from '@/app/lib/logger';

export async function POST(request: NextRequest) {
    try {
        // クライアントからJSONデータを取得
        const overtime = await request.json();
        const id = overtime.id;

        // 個別設定overtime.iniデータを作成
        const iniContents = await createIniContent(overtime);

        // カレンダーグループの個別設定overtime.iniデータの変更
        const updateResult = await ServerData.updateCalendarIniData(iniContents, id);

        if (updateResult === 0) {
            // 失敗のレスポンスを返す
            const errorInfo = `カレンダー[${id}]が存在しない。`
            Logger.info({
                message: errorInfo,
            });
            return NextResponse.json({ message: errorInfo }, { status: 400 });
        } else {
            // 成功のレスポンスを返す
            return NextResponse.json({ status: 200 });
        }

    } catch (error) {
        // エラーハンドリング関数でエラーを処理し、エラーメッセージを返す
        return handleApiError(error);
    }
}

// INIファイルを作成する関数
async function createIniContent(data: any) {
    // 基本カレンダーから「PC利用時間モニタの表示更新間隔」と「PC利用時間管理サーバのURL」を取得
    const baseCalendar = await ServerData.fetchBaseCalendars();
    const baseIniConfigs: Record<string, string> = {};
    if (baseCalendar && baseCalendar.length > 0) {
        const baseIniDat = iconv.decode(Buffer.from(baseCalendar[0].INI_DAT as Buffer), SJIS_ENCODING);
        baseIniDat.split('\n').forEach(line => {
            line = line.trim();
            if (line.includes('=')) {
                const [key, value] = line.split('=').map(part => part.trim());
                baseIniConfigs[key] = value.replaceAll('%n', '\n');
            } else {
                // 「=」がない場合、該当行が対象としない
            }
        });
    } else {
        // 基本カレンダーは必ず存在するため、取得できない場合の処理は不要です。
    }

    // PCの利用時間制限情報を設定
    const overtime = {
        WarningNotificationTime: '',        // PC利用停止直前状態移行時刻
        MonitorUpdateIntervalSec: '',       // PC利用時間モニタの表示更新間隔
        MonitorTextNormal: '',              // PC利用時間内表示テキスト
        MonitorTextWarning: '',             // PC利用停止直前表示テキスト
        MonitorTextTimeover: '',            // PC利用時間外表示テキスト
        ShutdownExtensionSec: '',           // PC停止猶予時間
        WarningDispText: '',                // 警告表示テキスト
        WarningDispIntervalTime: '',        // 警告表示間隔（HH:MM形式）
        WorkinghoursManageServerURL: '',    // PC利用時間管理サーバURL
        OvertimeMode: '',                   // PC動作モード
        UninstallPassword: '',              // アンインストール用パスワード
    };

    // 設定項目の代入
    overtime.WarningNotificationTime = data.shutdownBeforeTime;
    overtime.MonitorUpdateIntervalSec = baseIniConfigs['MonitorUpdateIntervalSec'];
    overtime.MonitorTextNormal = data.mointorText.replace(/(\n)/gm, '\\r\\n');
    overtime.MonitorTextWarning = data.shutdownBeforeText.replace(/(\n)/gm, '\\r\\n');
    overtime.MonitorTextTimeover = data.extrnalTimeText.replace(/(\n)/gm, '\\r\\n');
    overtime.ShutdownExtensionSec = data.secondsOfShutdown;
    overtime.WarningDispText = data.warningText.replace(/(\n)/gm, '\\r\\n');
    overtime.WarningDispIntervalTime = data.warningInterval;
    overtime.WorkinghoursManageServerURL = baseIniConfigs['WorkinghoursManageServerURL'];
    overtime.OvertimeMode = data.shutdownMode;
    overtime.UninstallPassword = data.uninstallPwd;

    // テンプレートファイルのパスを取得
    const templatePath = path.join(process.cwd(), 'app', OVERTIME_TEMPLATE_PATH, OVERTIME_TEMPLATE);

    // テンプレートファイルを読み込む
    const buffer = fs.readFileSync(templatePath);
    const template = iconv.decode(buffer, SJIS_ENCODING);

    // テンプレートに値を埋め込む
    const filledTemplate = fillTemplate(template, overtime);

    // SJIS形式にエンコードして返す
    const sjisBuffer = iconv.encode(filledTemplate, SJIS_ENCODING);

    return sjisBuffer;
}

// テンプレート文字列内のプレースホルダを値で置き換える関数
export function fillTemplate(template: string, values: { [key: string]: string }): string {
    return template.replace(/{(.*?)}/g, (_, key) => values[key] || '');
}
