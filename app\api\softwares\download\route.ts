/**
 * @file route.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { getContainerClient } from "@/app/lib/azureStorage";
import { ENV, PCSD_ERROR_MESSAGES } from "@/app/lib/definitions";
import { handleApiError } from "@/app/lib/portal-error";
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
    try {
        const params = await request.json();
        const containerClient = getContainerClient(ENV.SOFTWAREA_FILE_PATH);

        // 指定したファイル取得
        const blobClient = containerClient.getBlobClient(params.filePath);

        // ファイルが存在するか確認
        if (!(await blobClient.exists())) {
            return NextResponse.json({ success: false, message: PCSD_ERROR_MESSAGES.EMEC0014.replace("{0}", "ファイルのダウンロード") }, { status: 400 });
        } else {
            // ファイル存在場合、特に処理を行いません。
        }

        // ファイルのサイズを取得するためにファイルの統計情報を取得
        const properties = await blobClient.getProperties();
        // ZIPファイルをストリーミングするためのレスポンスヘッダーを準備
        const headers = new Headers({
            'Content-Type': properties.contentType!, // 正しいMIMEタイプに更新
            'Content-Disposition': `attachment;`,
            'Content-Length': `${properties.contentLength}`, // コンテンツの長さを設定
            'Transfer-Encoding': 'chunked',
            'Cache-Control': 'no-cache',
        });

        // ブロブの内容をストリームとして取得
        const downloadBlockBlobResponse = await blobClient.download(0);

        // ブロブの内容をストリームとして取得
        const blobStream = downloadBlockBlobResponse.readableStreamBody;

        return new NextResponse(blobStream as unknown as ReadableStream, {
            headers,
        });
    } catch (error) {
        return handleApiError(error);
    }
}
