/**
 * @file create-form.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

"use client";

import {
  allowedUploadCalendarFileNames,
  PCSD_ERROR_MESSAGES,
} from "@/app/lib/definitions";
import Tooltip from "@/app/ui/tooltip";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useState } from "react";
import FeedbackModal from "../feedback-modal";
import clsx from "clsx";
import { sanitize } from "isomorphic-dompurify";

export default function Form({
  id,
  groupname,
  page,
}: {
  id?: string;
  groupname?: string;
  page?: number;
}) {
  // グループ名をデコードする（未定義の場合は空文字列を設定）
  const decodeGrpName =
    groupname === undefined ? "" : sanitize(decodeURIComponent(groupname));

  // ファイル名を管理する状態を定義
  const [fileNames, setFileNames] = useState<string[]>([]);

  // アップロードされたファイルを管理する状態を定義
  const [files, setFiles] = useState<FileList | null>(null);

  // ファイルごとのエラーを管理する状態を定義（各ファイルのエラーをbooleanで表現）
  const [fileErrors, setFileErrors] = useState<boolean[]>([]);

  // XML検証エラーを管理する状態を定義（形式エラー、OTWWTサイズエラー、OTWサイズエラーを含む）
  const [xmlValidateErrors, setXmlValidateErrors] = useState<
    {
      isFormatError: boolean; // XML形式エラー
      isToolSizeError: boolean; // ツールファイルサイズエラー
      isOTWWTSizeError: boolean; // OTWWTサイズエラー
      isOTWSizeError: boolean; // OTWサイズエラー
    }[]
  >([]);

  // アップロードエラー全体を管理する状態を定義（エラーメッセージを文字列で保持）
  const [uploadErrors, setUploadErrors] = useState<string>("");

  // グループ名に関連するエラーを管理する状態を定義（エラーメッセージを文字列で保持）
  const [grpNameErrors, setGrpNameErrors] = useState<string>("");

  // ツールチップの表示状態を管理する（true: 表示中, false: 非表示）
  const [isTooltipVisible, setTooltipVisible] = useState(true);
  const [grpName, setGrpName] = useState(decodeGrpName);
  const [feedbackMsg, setFeedbackMsg] = useState<string | null>(null);
  const [feedbackError, setFeedbackError] = useState<string | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isMaskVisible, setIsMaskVisible] = useState(false);

  // Next.jsのルーターを取得
  const router = useRouter();

  // 検索パラメータをURLエンコードして取得
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams);

  // ファイル選択をクリックすると、ツールチップを非表示にする。
  const handleClick = (event: React.MouseEvent<HTMLInputElement>) => {
    setTooltipVisible(false);
  };
  // マウスがファイル選択から離れたとき、ツールチップのデフォルトCSSを復元する
  const handleMouseLeave = (event: React.MouseEvent<HTMLInputElement>) => {
    setTooltipVisible(true);
  };

  // ファイル選択オブジェクトの変更を発生させる処理
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    // ファイル選択イベントからファイルリストを取得
    const fileList = event.target.files;
    setXmlValidateErrors([]);
    setFileErrors([]);

    if (fileList) {
      // ファイル名を配列に変換し、状態として管理
      const names = Array.from(fileList).map((file) => file.name);
      setFileNames(names);

      // 選択されたファイルを状態として管理
      setFiles(event.target.files);

      // xlsm形式のファイルが複数選択されていないかを確認するためのフラグ
      let xlsmFileExisted = false;

      // 各ファイルのエラー状態を判定
      const errors = Array.from(fileList).map((file) => {
        // xlsm形式のファイルが既に存在している場合、エラーとする
        if (file.name.toLowerCase().endsWith(".xlsm") && xlsmFileExisted) {
          return true;
          // xlsm形式のファイルが初めて出現した場合、フラグを更新してエラーなし
        } else if (file.name.toLowerCase().endsWith(".xlsm")) {
          xlsmFileExisted = true;
          return false;
          // その他のファイルについて、許可されたファイル名リストに存在しない場合はエラーとする
        } else {
          return !allowedUploadCalendarFileNames.includes(file.name); // ファイル名が不正かどうかをチェック
        }
      });

      // 各ファイルのエラー状態を状態として管理
      setFileErrors(errors);

      // OTWCALEX01.xml～OTWCALEX04.xmlのファイル名前
      const otwElementsToCheck = allowedUploadCalendarFileNames.slice(1, 5);

      const validateXmlFiles = (fileList: FileList, errors: boolean[]) => {
        const fileValidationPromises = Array.from(fileList).map(
          (file, index) => {
            // ファイル名前は指定名前以外の場合、「許可されたファイル名を指定してください。」を出力するなので、xmlフォーマットとファイルサイズがチェックしない
            if (errors[index]) {
              return Promise.resolve({
                isFormatError: false,
                isToolSizeError: false,
                isOTWWTSizeError: false,
                isOTWSizeError: false,
              });
            } else {
              // 特に処理がない
            }

            // ツールファイルの場合、かつ、ファイルサイズが10485760バイトをお超える場合、falseを返す
            if (
              file.name.toLowerCase().endsWith(".xlsm") &&
              file.size > 10485760
            ) {
              return Promise.resolve({
                isFormatError: false,
                isToolSizeError: true,
                isOTWWTSizeError: false,
                isOTWSizeError: false,
              });
            }
            // OTWWTCAL.xml、かつ、ファイルサイズが31175バイトをお超える場合、falseを返す
            else if (
              file.name === allowedUploadCalendarFileNames[0] &&
              file.size > 31175
            ) {
              return Promise.resolve({
                isFormatError: false,
                isToolSizeError: false,
                isOTWWTSizeError: true,
                isOTWSizeError: false,
              });
            }
            // OTWCALEX01.xml～OTWCALEX04.xml、かつ、ファイルサイズが31175バイトをお超える場合、falseを返す
            else if (
              otwElementsToCheck.includes(file.name) &&
              file.size > 31127
            ) {
              return Promise.resolve({
                isFormatError: false,
                isToolSizeError: false,
                isOTWWTSizeError: false,
                isOTWSizeError: true,
              });
            } else {
              // 何のエラーがない
              return Promise.resolve({
                isFormatError: false,
                isToolSizeError: false,
                isOTWWTSizeError: false,
                isOTWSizeError: false,
              });
            }
          },
        );

        Promise.all(fileValidationPromises).then((results) => {
          setXmlValidateErrors(results); // 検証結果をセット
        });
      };

      validateXmlFiles(fileList, errors); // XMLファイルの検証を実行

      if (fileList.length != 6 && fileList.length != 0) {
        setUploadErrors(`${PCSD_ERROR_MESSAGES.EMEC0009}`);
      } else {
        setUploadErrors("");
      }
    } else {
      setFileNames([]);
      setFileErrors([]);
      setUploadErrors("");
    }
  };

  // カレンダー登録
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsMaskVisible(true);

    // アップロードされたファイルの数が6つでない場合は終了
    if (!files) {
      return;
    }

    // IDがある場合、カレンダー更新のため、グループ名をチェックしない
    if (!id) {
      const response = await fetch("/api/calendars/check", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ grpName }),
      });

      if (response.status === 401) {
        router.push("/login");
        return;
      } else {
        const group = await response.json();
        if (group.error) {
          setIsMaskVisible(false);
          showErrorModal(group.error);
          return;
        } else if (group.exists) {
          setIsMaskVisible(false);
          setGrpNameErrors(`${PCSD_ERROR_MESSAGES.EMEC0007}`);
          return;
        } else {
          setGrpNameErrors("");
        }
      }
    } else {
      // 特に処理がない
    }

    // FormDataを作成し、ファイルをFormDataに追加する。
    const formData = new FormData();
    for (let i = 0; i < files.length; i++) {
      formData.append("files", files[i]);
    }
    formData.append("grpName", grpName);
    formData.append("id", id ?? "");

    try {
      const response = await fetch("/api/calendars/upload", {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        await response.json();
        showMessageModal(
          PCSD_ERROR_MESSAGES.EMEC0015.replace("{0}", "カレンダーの登録"),
        );
      } else {
        if (response.status === 401) {
          router.push("/login");
          return;
        }
        // XMLフォーマット間違い場合
        else if (response.status === 422) {
          const data = await response.json();
          const fileCheckResults = await data.message;
          const fileErrors = await data.fileErrors;
          setFileErrors(fileErrors);
          setIsMaskVisible(false);
          setXmlValidateErrors(fileCheckResults);
        } else {
          showErrorModal(
            PCSD_ERROR_MESSAGES.EMEC0014.replace("{0}", "カレンダーの登録"),
          );
        }
      }
    } catch {
      showErrorModal(
        PCSD_ERROR_MESSAGES.EMEC0014.replace(
          "{0}",
          "カレンダー関連ファイルのアップロード",
        ),
      );
    } finally {
      setIsMaskVisible(false);
    }
  };

  const isValidInput = (str: string) => {
    const regex = /^[a-zA-Z0-9_\- .()]+$/;
    return regex.test(str);
  };

  // 戻るボタンの処理
  const goBack = () => {
    router.push(`/dashboard/calendars?${params.toString()}`);
  };

  const renderTableCells = () => {
    const cells = new Array(6)
      .fill("")
      .map((_, index) => fileNames[index] || "");
    return (
      <div className="flex mt-1">
        <div>
          {cells.map((name, index) => (
            <div className="flex" key={index}>
              <Tooltip text={name} torb="bottom">
                <input
                  id={"file" + index}
                  className={clsx(
                    "w-96 xl:w-96 2xl:w-[32rem] truncate rounded border border-gray-300 bg-gray-200 p-2 text-sm text-gray-400 focus:border-gray-300 focus:ring-gray-300",
                    {
                      "border-red-500":
                        fileErrors[index] ||
                        (xmlValidateErrors[index] &&
                          (xmlValidateErrors[index].isFormatError ||
                            xmlValidateErrors[index].isToolSizeError ||
                            xmlValidateErrors[index].isOTWWTSizeError ||
                            xmlValidateErrors[index].isOTWSizeError)),
                    },
                  )}
                  value={name}
                  disabled={true}
                />
              </Tooltip>
              <div
                className="flex flex-col justify-cente"
                style={{ justifyContent: "center" }}
              >
                {fileErrors[index] ? (
                  <span className="text-red-500 ml-2 text-sm">
                    {PCSD_ERROR_MESSAGES.EMEC0010}
                  </span> // 許可されたファイル名を指定してください。
                ) : xmlValidateErrors[index] &&
                  xmlValidateErrors[index].isFormatError ? (
                  <span className="text-red-500 ml-2 text-sm">
                    {PCSD_ERROR_MESSAGES.EMEC0011}
                  </span>
                ) : xmlValidateErrors[index] &&
                  xmlValidateErrors[index].isToolSizeError ? (
                  <span className="text-red-500 ml-2 text-sm">
                    {PCSD_ERROR_MESSAGES.EMEC0012.replace("{0}", "10MB")}
                  </span>
                ) : xmlValidateErrors[index] &&
                  xmlValidateErrors[index].isOTWWTSizeError ? (
                  <span className="text-red-500 ml-2 text-sm">
                    {PCSD_ERROR_MESSAGES.EMEC0012.replace(
                      "{0}",
                      "31,175バイト",
                    )}
                  </span>
                ) : xmlValidateErrors[index] &&
                  xmlValidateErrors[index].isOTWSizeError ? (
                  <span className="text-red-500 ml-2 text-sm">
                    {PCSD_ERROR_MESSAGES.EMEC0012.replace(
                      "{0}",
                      "31,127バイト",
                    )}
                  </span>
                ) : null}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const handleGrpName = (event: any) => {
    setGrpName(event.target.value);
    if (event.target.value !== "" && !isValidInput(event.target.value)) {
      setGrpNameErrors(
        PCSD_ERROR_MESSAGES.EMEC0008.replace("{0}", "カレンダーグループ名"),
      );
    } else {
      setGrpNameErrors("");
    }
  };

  // モーダルを開きメッセージを設定
  const showMessageModal = (newMessage: string) => {
    setFeedbackMsg(newMessage);
    setFeedbackError(null); // エラークリア
    setIsModalVisible(true); // モーダルを開く
  };
  // モーダルを開きエラーを設定
  const showErrorModal = (newError: string) => {
    setFeedbackError(newError);
    setFeedbackMsg(null); // メッセージクリア
    setIsModalVisible(true); // モーダルを開く
  };
  // モーダルを閉じる
  const closeModal = () => {
    const isFailed = !!feedbackError;
    setFeedbackError(null); // エラークリア
    setFeedbackMsg(null); // メッセージクリア
    setIsModalVisible(false);
    setIsMaskVisible(false);
    if (!isFailed) {
      // 操作が正常に完了し、ページが遷移する場合、前のページに戻る際に最初のページ（第１ページ）が表示され
      params.set("page", "1");
      router.push(`/dashboard/calendars?${params.toString()}`);
    }
  };

  // 登録ボタン無効化かどうか判断する
  const isFormValid = () => {
    return (
      !grpName ||
      !files ||
      (files && files.length === 0) ||
      !!grpNameErrors ||
      !!uploadErrors ||
      fileErrors.some((error) => error) ||
      xmlValidateErrors.some(
        (errObj) =>
          errObj.isFormatError ||
          errObj.isOTWSizeError ||
          errObj.isOTWWTSizeError ||
          errObj.isToolSizeError,
      )
    );
  };

  return (
    <form onSubmit={handleSubmit}>
      {isModalVisible && (
        <FeedbackModal
          message={feedbackMsg}
          error={feedbackError}
          onClose={closeModal}
        />
      )}
      {isMaskVisible && (
        <div className="fixed inset-0 bg-gray-900 opacity-50 z-40"></div>
      )}
      <div className="px-8 py-4 space-y-4 bg-white text-base font-medium">
        <div className="flex flex-col gap-4">
          <div className="flex items-center">
            <div className="w-48 xl:w-48 2xl:w-60 flex items-center justify-between flex-shrink-0">
              <label
                htmlFor="groupname"
                className="block text-sm font-medium text-gray-900"
              >
                カレンダーグループ名
              </label>
              <label className="text-red-500 me-2">*</label>
            </div>
            <input
              id="groupname"
              type="text"
              maxLength={64}
              className={clsx(
                "w-96 xl:w-96 2xl:w-[32rem] flex-shrink-0 rounded border border-gray-300 p-2 text-sm focus:border-gray-300 focus:ring-gray-300",
                {
                  "border-red-500": grpNameErrors,
                  "bg-gray-200 text-gray-400": !!groupname,
                  "bg-gray-50 text-gray-900": !groupname,
                },
              )}
              value={grpName}
              disabled={!!groupname}
              onChange={handleGrpName}
            />
            <span className="text-red-500 ml-2 text-sm">{grpNameErrors}</span>
          </div>
          <div className="flex items-center">
            <div className="w-48 xl:w-48 2xl:w-60 flex items-center justify-between">
              <label
                htmlFor="calendarfiles"
                className="block text-sm font-medium text-gray-900"
              >
                カレンダー関連ファイル
              </label>
              <label className="text-red-500 me-2">*</label>
            </div>
            <Tooltip
              text="以下６つのファイルを選択してください。<br><br>
 カレンダー作成ツール(*.xlsm) <br>
 OTWWTCAL.xml<br>
 OTWCALEX01.xml～OTWCALEX04.xml"
              style={{ width: "10px", left: "50%", fontSize: "14px" }}
              bigTip="big"
              torb="bottom"
              visible={isTooltipVisible}
            >
              <input
                type="file"
                id="fileInput"
                name="files"
                multiple
                onChange={handleFileChange}
                onClick={handleClick}
                onMouseLeave={handleMouseLeave}
                className={clsx(
                  "w-96 xl:w-96 2xl:w-[32rem] rounded mt-1 border border-gray-300 bg-gray-50 cursor-pointer focus:border-gray-300 focus:ring-gray-300",
                  {
                    "border-red-500": uploadErrors,
                  },
                )}
                title=""
              />
            </Tooltip>
            <span className="text-red-500 ml-2 text-sm"> {uploadErrors}</span>
          </div>
        </div>
        <div className="flex items-center" style={{ marginTop: "-1px" }}>
          <div className="w-48 xl:w-48 2xl:w-60 flex items-center flex-shrink-0">
            <label
              htmlFor="calendarfiles"
              className="block text-sm font-medium text-gray-900"
            >
              &nbsp;
            </label>
            <label className="text-red-500 me-2">&nbsp;</label>
          </div>

          {renderTableCells()}
        </div>
      </div>

      <div className="px-8 py-4 bg-gray-100 text-base">
        <div className="flex justify-end items-center">
          <button
            type="submit"
            className={`w-28 ms-3 rounded px-3 py-2 text-center text-xs font-medium text-transparent 
              drop-shadow-blue shadow-inner transition duration-300 
              ${isFormValid()
                ? "bg-gray-400 cursor-not-allowed opacity-50" // disabled css
                : "bg-gradient-blue hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80" // normal css
              }`}
            disabled={isFormValid()}
            style={{ backgroundImage: "url(/icons/btn_submit.ico)" }}
          >
            登録
          </button>
          <button
            type="button"
            onClick={goBack}
            className="w-28 ms-3 rounded bg-gradient-blue px-3 py-2 text-center text-xs font-medium text-transparent 
                  drop-shadow-blue shadow-inner hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
            style={{ backgroundImage: "url(/icons/btn_return.ico)" }}
          >
            戻る
          </button>
        </div>
      </div>
    </form>
  );
}
