/**
 * @file layout.tsx
 * @description ダッシュボードのレイアウトコンポーネント
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

"use client";

import Sidebar from "../ui/sidebar";
import Header from "../ui/header";
import Breadcrumb from "../ui/breadcrumb";
import useSession from "../hooks/use-session";
import { useRouter } from "next/navigation";
import { Suspense, useEffect } from "react";

// レイアウトコンポーネント
export default function Layout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const { session, isLoading } = useSession();

  // useEffect を使用して、セッションが有効かどうかを確認し、必要に応じてログインページにリダイレクトする
  useEffect(() => {
    if (!isLoading && !session.user?.userId) {
      // セッションがロード済みで、ユーザーIDが存在しない場合、ログインページにリダイレクト
      router.replace("/login");
    }
  }, [session, isLoading]);

  if (isLoading) {
    // セッションデータがまだロード中の場合、"Loading..." メッセージを表示
    return <p className="text-lg">Loading...</p>;
  }

  if (!session.user.userId) {
    // セッションがロード済みで、ユーザーIDが存在しない場合、null を返す（すでに useEffect でリダイレクト処理を行っているため）
    return null;
  } else {
    const sessionToken: string = document.cookie; // セッショントークンを取得
    document.cookie = `${sessionToken}; path=/; SameSite=None; Secure`; // 有効期限を削除
  }

  return (
    <>
      <Suspense fallback={<div>Loading...</div>}>
        <Header session={session} />
      </Suspense>
      <div className="flex bg-gray-900 h-[calc(100vh-6rem)]">
        <main className="order-2 flex-1 w-[calc(100vw-18rem)]">
          <div className="h-full flex flex-col py-2 pe-2">
            <Breadcrumb />
            <div className="flex-1 overflow-auto mt-2 bg-gradient-to-b from-0% from-[#898989] via-[#474747] via-50% to-[#323232] to-100%">
              <div className="p-2 h-full">
                <div className="bg-white h-full">{children}</div>
              </div>
            </div>
          </div>
        </main>
        <div className="order-1 w-72 flex-none">
          <Sidebar session={session} />
        </div>
      </div>
    </>
  );
}
