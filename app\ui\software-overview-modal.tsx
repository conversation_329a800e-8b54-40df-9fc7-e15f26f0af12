/**
 * @file software-overview-modal.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import React from "react";
import { useEffect, useState } from "react";
import FeedbackModal from "@/app/ui/feedback-modal";

// モーダルのプロパティ型を定義
type ModalProps = {
  onClose: () => void; // モーダルを閉じるための関数
  selectedSoftwareId: string; // 選択されたソフトウェアのID
};

// ソフトウェアデータの型を定義
type Software = {
  ID: string; // ソフトウェアのID
  SOFTWARE_OVERVIEW: string; // ソフトウェアの概要
};

// モーダルコンポーネントを定義
const Modal = ({ onClose, selectedSoftwareId }: ModalProps) => {
  // ソフトウェアデータを格納する状態
  const [software, setSoftware] = useState<Software>();

  // コンポーネントがマウントされた時や selectedSoftwareId が変更された時にデータを取得
  useEffect(() => {
    // ソフトウェアデータを取得する
    const fetchSoftware = async () => {
      if (selectedSoftwareId) {
        const responseAllCals = await fetch(
          "/api/softwares?id=" + selectedSoftwareId,
        );
        const data = await responseAllCals.json();
        if (responseAllCals.status === 500) {
          showErrorModal(data.error);
          return;
        } else {
          setSoftware(data);
        }
      }
    };
    fetchSoftware();
  }, [selectedSoftwareId]); // selectedSoftwareId が変更されるたびに実行

  const closeModal = () => {
    setSoftware(undefined);
    onClose && onClose();
  };

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [feedbackMsg, setFeedbackMsg] = useState<string | null>(null);
  const [feedbackError, setFeedbackError] = useState<string | null>(null);

  // モーダルを開きエラーを設定
  const showErrorModal = (newError: string) => {
    setFeedbackError(newError);
    setFeedbackMsg(null); // メッセージクリア
    setIsModalVisible(true); // モーダルを開く
  };

  // モーダルを閉じる
  const closeModal2 = () => {
    setFeedbackError(null); // エラークリア
    setFeedbackMsg(null); // メッセージクリア
    setIsModalVisible(false);
    onClose && onClose();
  };

  return (
    <div
      id="software-overview-modal"
      className="hidden overflow-y-auto overflow-x-hidden fixed inset-0 z-50 flex justify-center items-center"
    >
      {isModalVisible && (
        <FeedbackModal
          message={feedbackMsg}
          error={feedbackError}
          onClose={closeModal2}
        />
      )}
      <div className="relative w-full max-w-3xl lg:max-w-2xl xl:max-w-4xl 2xl:max-w-6xl max-h-full">
        <div className="relative rounded shadow bg-gray-600">
          <div className="flex items-center justify-between p-4 border-b rounded-t bg-gradient-header">
            <h3 className="text-lg font-semibold text-white">
              ソフトウェア概要
            </h3>
            <button
              type="button"
              className="text-gray-400 bg-transparent rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center hover:bg-gray-600 hover:text-white"
              onClick={closeModal}
            >
              <svg
                className="w-3 h-3"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 14 14"
              >
                <path
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                />
              </svg>
              <span className="sr-only">Close modal</span>
            </button>
          </div>
          <div className="h-96 p-4 space-y-4 bg-white text-sm font-medium overflow-y-auto">
            {software && software.SOFTWARE_OVERVIEW !== undefined ? (
              <div
                key={software.ID}
                className="whitespace-pre-line"
                dangerouslySetInnerHTML={{
                  __html: `${software.SOFTWARE_OVERVIEW}`,
                }}
              />
            ) : (
              ""
            )}
          </div>

          <div className="flex flex-row-reverse items-center p-4 border-t rounded-b bg-gradient-header gap-3">
            <button
              type="button"
              className="w-28 rounded bg-gradient-dark px-3 py-2 text-center text-xs font-medium text-transparent 
                  drop-shadow-dark shadow-dark hover:from-gray-700 hover:to-gray-700 focus:ring-gray-700 hover:opacity-80 transition duration-300"
              onClick={closeModal}
              style={{ backgroundImage: "url(/icons/btn_ok.ico)" }}
            >
              OK
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Modal;
