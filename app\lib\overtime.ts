/**
 * @file overtime.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

export interface Overtime {
  WarningNotificationTime: string;     // PC利用停止直前状態移行時刻
  MonitorUpdateIntervalSec?: number;     //PC利用時間モニタの表示更新間隔(秒単位) 
  MonitorTextNormal: string;            // PC利用時間内表示テキスト
  MonitorTextWarning: string;     // PC利用停止直前表示テキスト
  MonitorTextTimeover: string;        // PC利用時間外表示テキスト
  ShutdownExtensionSec: number;      // PC停止猶予時間数字
  OvertimeMode: string;           // 利用者PC動作モード
  WarningDispIntervalTime: string;        // 警告表示間隔時間
  WorkinghoursManageServerURL?: string     // PC利用時間管理サーバのURL
  WarningDispText: string;            // 警告表示テキスト
  UninstallPassword: string;           // アンインストールパスワード
}