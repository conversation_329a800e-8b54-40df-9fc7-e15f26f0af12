/**
 * @file route.tsx
 * @description カレンダーコピーAPIエンドポイント
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { NextRequest, NextResponse } from 'next/server';
import { handleApiError } from "@/app/lib/portal-error";
import ServerData from '@/app/lib/data';
import Logger from '@/app/lib/logger';
import { PCSD_ERROR_MESSAGES } from '@/app/lib/definitions';
import { formatMessage } from '@/app/lib/utils';

// コピー先の最大件数
const MAX_COPY_TARGETS = 100;

// リクエストボディの型定義
interface CopyCalendarRequest {
  selectedCalendarId: string;
  selectedItems: Array<{ ID: string }>;
}

/**
 * リクエストの検証を行う関数
 */
function validateRequest(body: CopyCalendarRequest): string | null {
  if (!body.selectedCalendarId) {
    return formatMessage(PCSD_ERROR_MESSAGES.EMEC0005, ["コピー元"]);
  }

  if (!body.selectedItems?.length) {
    return formatMessage(PCSD_ERROR_MESSAGES.EMEC0005, ["コピー先"]);
  }

  // コピー先の件数制限チェック
  if (body.selectedItems.length > MAX_COPY_TARGETS) {
    return formatMessage(PCSD_ERROR_MESSAGES.EMEC0019, [`${MAX_COPY_TARGETS}件`]);
  }

  return null;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json() as CopyCalendarRequest;

    // リクエストの検証
    const validationError = validateRequest(body);
    if (validationError) {
      return NextResponse.json(
        { error: validationError },
        { status: 400 }
      );
    }

    // selectedItems から ID のリストを抽出
    const selectedItemIds = body.selectedItems.map(item => item.ID);

    // 重複を排除
    const uniqueItemIds = Array.from(new Set(selectedItemIds));

    // 自分自身へのコピーを防止
    if (uniqueItemIds.includes(body.selectedCalendarId)) {
      return NextResponse.json(
        { error: formatMessage(PCSD_ERROR_MESSAGES.EMEC0001, ["コピー先"]) },
        { status: 400 }
      );
    }

    // 個別設定コピーの実行
    const success = await ServerData.copyCalendarIniData(
      body.selectedCalendarId,
      uniqueItemIds
    );

    if (!success) {
      const errorInfo = formatMessage(PCSD_ERROR_MESSAGES.EMEC0014, ["カレンダーのコピー"]);
      Logger.info({
        message: `${errorInfo} コピー元：${body.selectedCalendarId}, コピー先：${uniqueItemIds.join(",")}`,
      });
      return NextResponse.json(
        { error: errorInfo },
        { status: 400 }
      );
    }

    return NextResponse.json({ status: 200 });
  } catch (error) {
    return handleApiError(error);
  }
}