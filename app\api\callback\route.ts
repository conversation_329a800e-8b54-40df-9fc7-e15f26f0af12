/**
 * @file route.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import ServerData from "@/app/lib/data";
import Logger from "@/app/lib/logger";
import { handleApiError } from "@/app/lib/portal-error";
import { defaultSession, SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import jwt from 'jsonwebtoken';
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import { ENV } from "../../lib/definitions";

// POSTメソッドの実装
export async function POST(req: NextRequest) {
    // keycloak登録情報取得
    const { code } = await req.json();
    try {

        // KEYCLOAKのREALM
        const realm = ENV.KEYCLOAK_REALM;
        // KEYCLOAKのCLIENT
        const clientId = ENV.KEYCLOAK_CLIENT;
        // KEYCLAOKログイン成功後のコールバックアドレス
        const redirectUri = ENV.KEYCLOAK_REDIRECT_URL;
        // KEYCLOAKサービスドメイン
        const domainName = ENV.KEYCLOAK_INTERNAL_DOMAIN_NAME;
        // KEYCLOAKサーバのクライアント認証器
        const clientSecret = ENV.KEYCLOAK_CLIENT_SECRET;

        if (!realm || !clientId || !redirectUri || !domainName || !clientSecret) {
            Logger.info({
                message: "環境変数の取得に失敗しました。",
            });
            return NextResponse.json({ status: 400 });
        }

        // アクセストークン要求URLの構築
        const tokenUrl = `${domainName}/realms/${realm}/protocol/openid-connect/token`;
        // API呼び出しパラメータ作成
        const params = new URLSearchParams();
        // 権限タイプ
        params.append('grant_type', 'authorization_code');
        // 許可コード
        params.append('code', code);
        // KEYCLOAKのCLIENT
        params.append('client_id', clientId);
        // Keycloakサーバのクライアント認証器
        params.append('client_secret', clientSecret);
        // KEYCLAOKログイン成功後のコールバックアドレス
        params.append('redirect_uri', redirectUri);
        // BufferオブジェクトをBase 64符号化文字列に変換する
        const basicAuth = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');
        // オブジェクトheadersを作成する
        const headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': `Bearer ${basicAuth}`
        };

        // APIを呼び出してaccess_tokenを取得する
        const response = await fetch(tokenUrl, {
            method: 'POST',
            headers: headers,
            body: params.toString(),
        });

        // API呼び出しに失敗
        if (!response.ok) {
            Logger.info({
                message: "access_tokenの取得に失敗しました。",
            });
            return NextResponse.json({ status: 400 });
        }
        // 取得結果json形式変換
        const data = await response.json();

        // JWT解析で取得したAccess_tokenによるユーザー情報の取得
        const payload = jwt.decode(data.access_token) as jwt.JwtPayload;

        // セッションオブジェクトの初期化
        const session = await getIronSession<SessionData>(cookies(), sessionOptions);
        if (!session.user) {
            // userオブジェクトの初期化
            session.user = { ...defaultSession.user };
        } else {
            // userオブジェクトがある場合、特に処理を処理を行いません。
        }

        session.user.id = payload.sub ?? '';
        session.user.userId = payload.preferred_username;
        session.user.schemaName = payload.schema_name;
        [session.user.schemaUser, session.user.schemaPwd] = await ServerData.getSchemaUserPwd(session.user.schemaName) as [string, string];

        const timezone = req.headers.get('Time-Zone');
        session.user.tz = timezone ? timezone : Intl.DateTimeFormat().resolvedOptions().timeZone;

        session.user.refreshToken = data.refresh_token;
        await session.save();
        return NextResponse.json({ status: 200 });
    } catch (error) {
        // エラーが発生した場合はエラーハンドリング関数に委譲
        return handleApiError(error);
    }
}