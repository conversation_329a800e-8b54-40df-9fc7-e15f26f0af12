/**
 * @file page.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { fetchTasksListPages } from "@/app/lib/data/tasks";
import PageSize from "@/app/ui/page-size";
import Pagination from "@/app/ui/pagination";
import { TableSkeleton } from "@/app/ui/skeletons";
import Table from "@/app/ui/tasks/table";
import { Suspense } from "react";


export default async function Page({
  searchParams,
}: {
  searchParams?: {
    page?: string;
    size?: string;
    sort?: string;
    order?: "asc" | "desc";
  };
}) {
  // sizeParamを取得し、10、30、50のいずれかであればそのまま使用し、そうでない場合は10を設定
  const sizeParam = Number(searchParams?.size) || 10;
  const size = [10, 30, 50].includes(sizeParam) ? sizeParam : 10;

  // pageParamを取得し、無効な場合は1をデフォルト設定
  const pageParam = Number(searchParams?.page) || 1;

  // ソート順を取得し、指定がなければデフォルトで"STARTED_AT"を設定
  const sort = searchParams?.sort || "STARTED_AT";
  // ソート順序を取得し、指定がなければデフォルトで"desc"を設定
  const order = searchParams?.order || "desc";

  // ページ数を取得し、無効な場合は1をデフォルト設定
  const totalPages =
    (await fetchTasksListPages(size)) || 0;
  console.log(`Total Pages: ${totalPages}`);

  // 現在のページ番号を設定。1未満なら1、totalPagesを超えていればtotalPagesに設定
  const currentPage = Math.min(Math.max(pageParam, 1), totalPages);

  return (
    <div className="p-4 h-full flex flex-col space-y-3">
      <div className="flex-column flex flex-wrap items-center justify-between rounded-t-xl">
        {totalPages > 0 && (
          <div className="flex items-center">
            <Pagination totalPages={totalPages} />
            <PageSize />
          </div>
        )}
      </div>
      <div className="relative overflow-x-auto overflow-y-auto shadow-md rounded-t-lg">
        <Suspense
          key={currentPage + size + sort + order}
          fallback={<TableSkeleton />}
        >
          <Table page={currentPage} size={size} sort={sort} order={order} />
        </Suspense>
      </div>
    </div>
  );
}
