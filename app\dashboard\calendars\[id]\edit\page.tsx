/**
 * @file page.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import ServerData from "@/app/lib/data";
import Form from "@/app/ui/calendars/edit-form";
import { Overtime } from "@/app/lib/overtime";
import iconv from "iconv-lite";
import { SJIS_ENCODING } from "@/app/lib/definitions";

export default async function Page({
  params,
  searchParams,
}: {
  params: { id: string };
  searchParams: { page: string };
}) {
  const id = params.id;
  const calendar = await ServerData.fetchCalendarInidataById(id);

  const overtime: Overtime = {
    WarningNotificationTime: "", // PC利用停止直前状態移行時刻
    MonitorTextNormal: "", // PC利用時間内表示テキスト
    MonitorTextWarning: "", // PC利用停止直前表示テキスト
    MonitorTextTimeover: "", // PC利用時間外表示テキスト
    ShutdownExtensionSec: 0, // PC停止猶予時間数字
    OvertimeMode: "", // 利用者PC動作モード
    WarningDispIntervalTime: "", // 警告表示間隔時間 注意：「HH」が必要。（05:33 → ok、5:33 → ng）
    WarningDispText: "", // 警告表示テキスト
    UninstallPassword: "", // アンインストール
  };

  const iniConfigs: Record<string, string> = {};
  // 個別設定overtime.iniデータ
  let iniDat: Buffer;

  // 「個別設定overtime.iniデータ」が存在場合、クライアント設定画面でDBから選択した個別設定を表示する
  if (calendar && calendar.INI_DAT) {
    iniDat = calendar?.INI_DAT as Buffer;
  } else {
    // 取得した個別設定overtime.iniデータがNULL場合、基本カレンダーの個別設定overtime.iniデータを取得し、設定画面で表示する
    const [baseCalendar] = await Promise.all([ServerData.fetchBaseCalendars()]);
    iniDat = baseCalendar![0].INI_DAT as Buffer;
  }

  // calendar.INI_DAT を SJIS エンコーディングでデコードして文字列に変換
  const iniContent = iconv.decode(Buffer.from(iniDat), SJIS_ENCODING);

  // 改行で分割して各行を処理
  iniContent.split("\n").forEach((line) => {
    line = line.trim(); // 行の前後の空白を削除
    if (line.includes("=")) {
      // '=' でキーと値に分割し、前後の空白を削除
      const [key, value] = line.split("=").map((part) => part.trim());
      // 改行コードを置換して iniConfigs に追加
      iniConfigs[key] = value.replaceAll("\\r\\n", "\n");
    } else {
      // 改行存在しない場合、特に処理を行いません。
    }
  });

  overtime.WarningNotificationTime = iniConfigs["WarningNotificationTime"]; // PC利用停止直前状態移行時刻
  overtime.MonitorTextNormal = iniConfigs["MonitorTextNormal"]; // PC利用時間内表示テキスト
  overtime.MonitorTextWarning = iniConfigs["MonitorTextWarning"]; // PC利用停止直前表示テキスト
  overtime.MonitorTextTimeover = iniConfigs["MonitorTextTimeover"]; // PC利用時間外表示テキスト
  overtime.ShutdownExtensionSec = Number(iniConfigs["ShutdownExtensionSec"]); // PC停止猶予時間数字
  overtime.OvertimeMode = iniConfigs["OvertimeMode"]; // 利用者PC動作モード
  overtime.WarningDispIntervalTime = iniConfigs["WarningDispIntervalTime"]; // 警告表示間隔時間
  overtime.WarningDispText = iniConfigs["WarningDispText"]; // 警告表示テキスト
  overtime.UninstallPassword = iniConfigs["UninstallPassword"]; // アンインストールパスワード

  return (
    <div className="p-4 h-full flex flex-col space-y-3 overflow-y-auto overflow-hidden">
      <Form overtime={overtime} id={id} page={searchParams.page} />
    </div>
  );
}
