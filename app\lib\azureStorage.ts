/**
 * @file azureStorage.ts
 * @description Azure Storage に関するユーティリティ関数の定義
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { BlobServiceClient, ContainerClient } from "@azure/storage-blob";
import { ENV } from "./definitions";

// シングルトンパターン: BlobServiceClient
let blobServiceClient: BlobServiceClient | null = null;

// ContainerClient のキャッシュ
const containerClients: Map<string, ContainerClient> = new Map();

// BlobServiceClient の初期化
function getBlobServiceClient(): BlobServiceClient {
  if (!ENV.AZURE_STORAGE_CONNECTION_STRING) {
    throw new Error("Azure Storageの接続文字列が見つかりません。");
  }

  if (!blobServiceClient) {
    blobServiceClient = BlobServiceClient.fromConnectionString(ENV.AZURE_STORAGE_CONNECTION_STRING!);
  }
  return blobServiceClient;
}

// ContainerClient の取得または作成
export function getContainerClient(containerName: string): ContainerClient {
  if (!containerClients.has(containerName)) {
    const client = getBlobServiceClient().getContainerClient(containerName);
    containerClients.set(containerName, client);
  }
  return containerClients.get(containerName)!; // 非空断言を使用して、取得した値が存在することを保証
}
