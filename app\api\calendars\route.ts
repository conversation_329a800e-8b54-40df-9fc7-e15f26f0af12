/**
 * @file route.ts
 * @description カレンダーAPIエンドポイント - 異なるタイプのカレンダーを取得する
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { NextResponse, type NextRequest } from 'next/server';
import ServerData from '@/app/lib/data';
import { handleApiError } from "@/app/lib/portal-error";
import { ApiValidator, type PaginationParams } from '@/app/lib/api-validator';

// ダイナミックルーティングを有効化
export const dynamic = 'force-dynamic';

// 有効なソートフィールドの定義
const VALID_SORT_FIELDS = ['GROUP_NAME', 'CALENDAR_TOOL_NAME', 'UPLOAD_DATE', 'INI_DAT', 'CLIENT_CONFIG_UPDATE'];

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;

    // 基本カレンダーの選択パラメータを取得
    const isBase = searchParams.get('isBase');
    // すべてのカレンダー（基本カレンダー以外）の選択パラメータを取得
    const isAll = searchParams.get('isAll');

    // 基本カレンダーのリクエスト処理
    if (isBase === '1') {
      const data = await ServerData.fetchBaseCalendars();
      return NextResponse.json(data);
    }

    // すべてのカレンダーのリクエスト処理
    if (isAll === '1') {
      const data = await ServerData.fetchAllCalendarsButBase();
      return NextResponse.json(data);
    }

    // ページネーションパラメータを取得
    const paginationParams: PaginationParams = {
      size: searchParams.get('size'),
      page: searchParams.get('page'),
      sort: searchParams.get('sort'),
      order: searchParams.get('order'),
    };

    // パラメータを検証
    const validationError = ApiValidator.validatePaginationParams(
      paginationParams,
      VALID_SORT_FIELDS
    );
    if (validationError) return validationError;

    // デフォルト値を設定して処理を実行
    const { size, page, sort, order } = ApiValidator.setDefaultPaginationParams(paginationParams);
    const data = await ServerData.fetchCalendars(size, page, sort, order);

    return NextResponse.json(data);
  } catch (error) {
    return handleApiError(error);
  }
}