/**
 * @file route.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { NextRequest, NextResponse } from 'next/server';
import ServerData from "@/app/lib/data";
import { handleApiError } from "@/app/lib/portal-error";

// `GET` リクエストを処理する関数
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // URL パラメータから ID を取得
    const { id } = params;

    const [calendarRecord] = await Promise.all([
      ServerData.fetchCalendaToolInfoById(id)
    ]);

    // カレンダー作成ツールデータをBase64に変換
    const fileData = calendarRecord!.CALENDAR_TOOL_DATA.toString('base64');
    const fileName = calendarRecord!.CALENDAR_TOOL_NAME;

    // レスポンス
    const response = NextResponse.json({
      success: true,
      fileName: fileName,
      fileData: fileData,
    });

    // ダウンロード用のヘッダー設定
    response.headers.set('Content-Disposition', `attachment; filename="${encodeURIComponent(fileName)}"`);
    response.headers.set('Content-Type', 'application/json');

    return response;
  } catch (error) {
    // エラーハンドリング関数でエラーを処理し、エラーメッセージを返す
    return handleApiError(error);
  }
}