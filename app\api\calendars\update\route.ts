/**
 * @file route.tsx
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import { NextResponse } from 'next/server';
import ServerData from '@/app/lib/data';
import { handleApiError } from "@/app/lib/portal-error";

// ダイナミックルーティング
export const dynamic = 'force-dynamic';

export async function GET(req: Request) {
    try {
        const url = new URL(req.url);

        // 個別設定解除 またはカレンダー削除フラグ
        const flg = url.searchParams.get('flg');
        // カレンダーID
        const id = url.searchParams.get('id');
        // 個別設定解除/カレンダー削除を行う
        const result = await ServerData.updateCalendar(flg!, id!);

        return NextResponse.json(result);
    } catch (error) {
        // エラーハンドリング関数でエラーを処理し、エラーメッセージを返す
        return handleApiError(error);
    }
}