/**
 * @file data.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

"use server";

import prisma from "@/app/lib/prisma";
import Logger, { LogFunctionSignature } from "./logger";
import { ENV } from "./definitions";
import { handleServerError } from "./portal-error";
import { toUperCaseKeys } from "@/app/lib/utils"
import { Prisma } from '@prisma/client';
import { SessionData, sessionOptions } from "@/app/lib/session";
import { getIronSession } from "iron-session";
import { cookies } from "next/headers";
import { PrismaClient } from '@prisma/client';
import { formatDate } from "./utils";
import { v4 as uuidv4 } from 'uuid';
import sanitizeHtml from 'sanitize-html';

type SOFTWARE_DOWNLOAD_INFO = Prisma.SOFTWARE_DOWNLOAD_INFOGetPayload<{}>;


/**
 * カレンダーオブジェクトの配列を変換し、ユーザーのタイムゾーンに基づいて日付をフォーマットする
 * 
 * @param calendars - 変換するカレンダーオブジェクトの配列
 * @param user - ユーザー情報（タイムゾーン情報を含む）
 * @returns 変換後のカレンダーオブジェクトの配列
 */
function transformCalendars(calendars: any[], user: any) {
  return calendars.map((calendar) => {
    return {
      ...calendar,
      // UPLOAD_DATE をユーザーのタイムゾーンに基づいてフォーマットする
      UPLOAD_DATE_TZ: formatDate(calendar.UPLOAD_DATE, user.tz),
      // CLIENT_CONFIG_UPDATE が存在する場合、ユーザーのタイムゾーンでフォーマットする
      CLIENT_CONFIG_UPDATE_TZ: calendar.CLIENT_CONFIG_UPDATE ? formatDate(calendar.CLIENT_CONFIG_UPDATE, user.tz) : null,
    };
  });
}
/**
 * ソート条件を変換し、SQLのORDER BY句用の文字列を生成する。
 * 
 * この関数は、指定されたソートフィールドとソート順序をもとに、
 * SQLのORDER BY句に使用できる文字列を生成します。
 * 特に、'ini_dat'フィールドの場合は、値の有無（NULLかどうか）を基準にソートを実現します。
 * また、ソートフィールドが'group_name'でない場合は、
 * 指定された第２ソートキーを追加します。
 * 
 * @param sort - ソートするフィールド名
 * @param order - ソート順序（'asc'または'desc'）
 * @param convertStr - 'ini_dat'フィールドの有無を示す仮想フィールド名（例: 'ini_dat_exists'）
 * @param secondSortKey - 第２ソートキー（例: 'group_name'）,ソート順序は'asc'で固定する
 * @returns SQLのORDER BY句用の文字列
 */
function formatSortConditions(
  sort: string,
  order: string,
  convertStr: string,
  secondSortKey: string
): string {
  const sortLowerStr = sort.toLowerCase();
  const orderLowerStr = order.toLowerCase();

  let orderByStr = '';

  // ソートフィールドが'ini_dat'の場合、値の有無でソートする
  if (sortLowerStr === 'ini_dat') {
    orderByStr = `${convertStr} ${orderLowerStr}`;
  } else {
    // その他のフィールドは通常のソートを行う
    orderByStr = `${sortLowerStr} ${orderLowerStr}`;
  }

  // ソートフィールドが第２ソートキーでない場合は、指定された第２ソートキー条件を追加する
  if (sortLowerStr !== secondSortKey) {
    orderByStr += `, ${secondSortKey} asc`;
  }

  return orderByStr;
}


const prismaClients: Record<string, PrismaClient> = {};
// 異なる接続ごとに、それぞれの接続が単一のインスタンスであることを保証する
const getPrismaClient = (databaseUrl: string) => {
  if (!prismaClients[databaseUrl]) {
    prismaClients[databaseUrl] = new PrismaClient({
      datasources: { db: { url: databaseUrl } },
    });
  }
  return prismaClients[databaseUrl];
};

/**
 * 大文字ソート条件を変換し、SQLのORDER BY句用の文字列を生成する。
 * 
 * この関数は、指定されたソートフィールドとソート順序をもとに、
 * SQLのORDER BY句に使用できる文字列を生成します。
 * 特に、指定されたフィールドが存在するかどうかを基準にソートを実現します。
 * また、ソートフィールドが第2ソートキーと異なる場合は、
 * 指定された第2ソートキーを追加します。
 * 
 * @param sort - ソートするフィールド名
 * @param order - ソート順序（'asc'または'desc'）
 * @param secondSortKeyUper - 第2ソートキー（例: 'SOFTWARE_NAME'）。ソート順序は'asc'で固定します。
 * @returns SQLのORDER BY句用の文字列
 * 
 * @remarks
 * - ソートフィールド名と第2ソートキーは、内部的に大文字に変換されます。
 * - 生成されたORDER BY句は、大文字のフィールド名を使用します。
 */
function formatSortConditionsUperCase(
  sort: string,
  order: string,
  secondSortKeyUper: string
): string {
  const sortUperStr = sort.toUpperCase();
  const orderLowerStr = order.toLowerCase();

  let orderByStr = `"${sortUperStr}" ${orderLowerStr}`;

  // ソートフィールドが第2ソートキーでない場合は、指定された第2ソートキーを追加する
  if (sortUperStr !== secondSortKeyUper) {
    orderByStr += `, "${secondSortKeyUper}" asc`;
  }

  return orderByStr;
}

/**
 * 操作ログーデータ操作クラス
 */
class ServerData {

  // #region PC自動シャットダウン
  // スキーマ名前により、commonスキーマから、スキーマの登録ユーザーとパスワードを非同期で取得するメソッド
  @LogFunctionSignature()
  static async getSchemaUserPwd(schemaName: string) {
    const whereClause: any = {};
    // Keycloakから渡されたスキーマ名前です
    whereClause.SCHEMA_NAME = schemaName;
    // スキーマにアクセスできるユーザー名とパスワードを取得する
    const schemaUserFromKeycloak = await prisma.sCHEMA_USER.findFirst({
      where: whereClause,
    });

    return [schemaUserFromKeycloak?.SCHEMA_USER, schemaUserFromKeycloak?.SCHEMA_PWD];
  }

  // #region ソフトウェアダウンロード
  // フィルタされたソフトウェアのリストを非同期で取得するメソッド
  @LogFunctionSignature()
  static async fetchSoftwares(
    size: number,
    page: number,
    sort: string,
    order: string,
  ) {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);

      // ソート条件文字列を動的に生成する
      const orderByStr = formatSortConditionsUperCase(sort, order, "SOFTWARE_NAME");

      // SQL文を作成し、ソフトウェアダウンロード情報を取得する
      const softwares = await prisma.$queryRawUnsafe<SOFTWARE_DOWNLOAD_INFO[]>(`
        SELECT
            SDI.* 
        FROM
            "CUSTOMER_SOFTWARE_DOWNLOAD" CSD 
            INNER JOIN "SOFTWARE_DOWNLOAD_INFO" SDI 
                ON  CSD."SOFTWARE_NAME" = SDI."SOFTWARE_NAME"
                AND CSD."VERSION_INFO" = SDI."VERSION_INFO"
                AND CSD."SOFTWARE_UPDATE_DATE" = SDI."SOFTWARE_UPDATE_DATE"
                AND SDI."ENABLED_FLAG" = '1'
        WHERE
          CSD."SCHEMA_NAME"=$1
        ORDER BY ${orderByStr}
        LIMIT ${size} 
        OFFSET ${(page - 1) * size} 
          ;
        `,
        session.user.schemaName
      );

      const cachedSoftwares = softwares.map((software) => {
        return {
          ...software,
          SOFTWARE_UPDATE_YYYYMMDD: formatDate(software.SOFTWARE_UPDATE_DATE, session.user.tz).slice(0, 10),
        };
      });
      return cachedSoftwares;
    } catch (error) {
      handleServerError(error);
    }
  }

  // ソフトウェアのリストを非同期で取得するメソッド
  @LogFunctionSignature()
  static async fetchSoftwareById(
    id: string,
  ) {
    try {
      if (!id) return [];
      const softwares = await prisma.$queryRawUnsafe<SOFTWARE_DOWNLOAD_INFO[]>(`
        SELECT
            SDI."ID", SDI."SOFTWARE_OVERVIEW" 
        FROM
            "SOFTWARE_DOWNLOAD_INFO" SDI 
        WHERE  SDI."ID" = $1::uuid
          AND SDI."ENABLED_FLAG" = '1'
        ORDER BY
          "SOFTWARE_NAME" ASC
          ;
        `,
        id
      );

      return softwares[0];

    } catch (error) {
      handleServerError(error);
    }
  }

  // ソフトウェアページ数を非同期で取得するメソッド
  @LogFunctionSignature()
  static async fetchSoftwarePages(
    size: number,
  ) {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);

      const softwaresCnt = await prisma.$queryRawUnsafe<{ cnt: number }[]>(`
        SELECT COUNT(CSD.*) AS cnt
        FROM
            "CUSTOMER_SOFTWARE_DOWNLOAD" CSD 
            INNER JOIN "SOFTWARE_DOWNLOAD_INFO" SDI 
                ON  CSD."SOFTWARE_NAME" = SDI."SOFTWARE_NAME"
                AND CSD."VERSION_INFO" = SDI."VERSION_INFO"
                AND CSD."SOFTWARE_UPDATE_DATE" = SDI."SOFTWARE_UPDATE_DATE"
                AND SDI."ENABLED_FLAG" = '1'
        WHERE
          CSD."SCHEMA_NAME"=$1
          ;
        `, session.user.schemaName);

      const count = Number(softwaresCnt[0]?.cnt || 0);
      return Math.ceil(count / size);

    } catch (error) {
      handleServerError(error);
    }
  }
  // #endregion ソフトウェアダウンロード

  // #region カレンダー
  // カレンダーページ数を非同期で取得するメソッド
  @LogFunctionSignature()
  static async fetchCalendarsPages(
    size: number,
  ) {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);
      // データベースにリンクする文字列を動的に生成する
      const databaseUrl = `postgresql://${session.user.schemaUser}:${session.user.schemaPwd}@${ENV.PGSQL_HOST}:${ENV.PGSQL_PORT}/${ENV.PGSQL_DATABASE_WORKFLOW}`;
      const schemaPrisma = getPrismaClient(databaseUrl);

      const calendars = await schemaPrisma.$queryRawUnsafe<{ cnt: number }[]>(`
        SELECT COUNT(*) AS cnt
        FROM jcpotw_group_table
        WHERE enabled_flag = '1'
              AND group_name IS NOT NULL
          ;
        `);
      const calendarsCount = Number(calendars[0]?.cnt || 0);
      return Math.ceil(calendarsCount / size);

    } catch (error) {
      handleServerError(error);
    }
  }

  // フィルタされたカレンダーのリストを非同期で取得するメソッド
  @LogFunctionSignature()
  static async fetchCalendars(
    size: number,
    page: number,
    sort: string,
    order: string,
  ) {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);

      // データベースにリンクする文字列を動的に生成する
      const databaseUrl = `postgresql://${session.user.schemaUser}:${session.user.schemaPwd}@${ENV.PGSQL_HOST}:${ENV.PGSQL_PORT}/${ENV.PGSQL_DATABASE_WORKFLOW}`;
      const schemaPrisma = getPrismaClient(databaseUrl);

      // ini_dat有無の名称再定義
      const convertIniStr = "ini_dat_isnull";
      // ソート条件文字列を動的に生成する
      const orderByStr = formatSortConditions(sort, order, convertIniStr, "group_name");

      const calendars = await schemaPrisma.$queryRawUnsafe<any[]>(`
        SELECT
          id,
          group_name,
          calendar_tool_name,
          upload_date,
          CASE
            WHEN ini_dat IS NOT NULL THEN 0
            ELSE 1
          END AS ${convertIniStr},
          client_config_update
        FROM jcpotw_group_table
        WHERE enabled_flag = '1'
              AND group_name IS NOT NULL
        ORDER BY ${orderByStr}
        LIMIT ${size} 
        OFFSET ${(page - 1) * size} 
          ;
        `);
      const uperCalendars = calendars.map(toUperCaseKeys);

      return transformCalendars(uperCalendars, session.user);
    } catch (error) {
      handleServerError(error);
    }
  }

  // 基本カレンダーのリストを非同期で取得するメソッド
  @LogFunctionSignature()
  static async fetchBaseCalendars(
  ) {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);

      // データベースにリンクする文字列を動的に生成する
      const databaseUrl = `postgresql://${session.user.schemaUser}:${session.user.schemaPwd}@${ENV.PGSQL_HOST}:${ENV.PGSQL_PORT}/${ENV.PGSQL_DATABASE_WORKFLOW}`;
      const schemaPrisma = getPrismaClient(databaseUrl);

      const calendars = await schemaPrisma.$queryRawUnsafe<any[]>(`
        SELECT
          JGT.id,
          JGT.group_name,
          JGT.calendar_tool_name,
          JGT.upload_date,
          JGT.ini_dat,
          JGT.client_config_update
        FROM
            jcpotw_group_table JGT 
        WHERE  JGT.group_name IS NULL
          AND JGT.enabled_flag = '1'
          ;
        `);
      const uperCalendars = calendars.map(toUperCaseKeys);

      return transformCalendars(uperCalendars, session.user);
    } catch (error) {
      handleServerError(error);
    }
  }

  // IDによりカレンダーの指定情報を非同期で取得するメソッド
  @LogFunctionSignature()
  private static async fetchCalendarById(
    id: string,
    fetchColumnData: string
  ) {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);

      // データベースにリンクする文字列を動的に生成する
      const databaseUrl = `postgresql://${session.user.schemaUser}:${session.user.schemaPwd}@${ENV.PGSQL_HOST}:${ENV.PGSQL_PORT}/${ENV.PGSQL_DATABASE_WORKFLOW}`;
      const schemaPrisma = getPrismaClient(databaseUrl);

      const calendars = await schemaPrisma.$queryRawUnsafe<any[]>(`
        SELECT
            ${fetchColumnData}
        FROM
            jcpotw_group_table JGT 
        WHERE  JGT.id = $1::uuid
          AND JGT.enabled_flag = '1'
          ;
        `,
        id
      );
      const calendar = toUperCaseKeys(calendars[0]);

      return calendar;
    } catch (error) {
      handleServerError(error);
    }
  }

  // IDによりカレンダーのini_datを非同期で取得するメソッド
  @LogFunctionSignature()
  static async fetchCalendarInidataById(
    id: string
  ) {
    const fetchIniDatColumn = "JGT.ini_dat"

    const calendar = this.fetchCalendarById(id, fetchIniDatColumn);
    return calendar;
  }

  // IDによりカレンダーのをcalendar_tool情報非同期で取得するメソッド
  @LogFunctionSignature()
  static async fetchCalendaToolInfoById(
    id: string
  ) {
    const fetchCalendarToolColumn = "JGT.calendar_tool_name, JGT.calendar_tool_data"

    const calendar = this.fetchCalendarById(id, fetchCalendarToolColumn);
    return calendar;
  }
  // 基本カレンダー以外のすべてカレンダーを非同期で取得するメソッド（カレンダーコピー用）
  @LogFunctionSignature()
  static async fetchAllCalendarsButBase() {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);

      // データベースにリンクする文字列を動的に生成する
      const databaseUrl = `postgresql://${session.user.schemaUser}:${session.user.schemaPwd}@${ENV.PGSQL_HOST}:${ENV.PGSQL_PORT}/${ENV.PGSQL_DATABASE_WORKFLOW}`;
      const schemaPrisma = getPrismaClient(databaseUrl);

      const calendars = await schemaPrisma.$queryRawUnsafe<any>(`
        SELECT
            JGT.id, JGT.group_name 
        FROM
            jcpotw_group_table JGT 
        WHERE  JGT.group_name IS NOT NULL
          AND JGT.enabled_flag = '1'
        ORDER BY 
          JGT.group_name
          ;
        `);
      const uperCalendars = calendars.map(toUperCaseKeys);
      return uperCalendars;

    } catch (error) {
      handleServerError(error);
    }
  }

  // 個別設定解除/カレンダー削除
  @LogFunctionSignature()
  static async updateCalendar(
    flg: string,
    id: string,
  ) {
    const session = await getIronSession<SessionData>(cookies(), sessionOptions);

    let updateResult;
    try {

      // データベースにリンクする文字列を動的に生成する
      const databaseUrl = `postgresql://${session.user.schemaUser}:${session.user.schemaPwd}@${ENV.PGSQL_HOST}:${ENV.PGSQL_PORT}/${ENV.PGSQL_DATABASE_WORKFLOW}`;
      const schemaPrisma = getPrismaClient(databaseUrl);

      // 個別設定解除
      if (flg === 'delIniDat') {
        updateResult = await schemaPrisma.$executeRawUnsafe(`
          UPDATE 
            jcpotw_group_table
          SET 
            ini_dat = NULL
            , client_config_update = NOW() AT TIME ZONE 'UTC'
            , update_date = NOW() AT TIME ZONE 'UTC'
            , update_user = $1
          WHERE id = $2::uuid;
        `,
          session.user.userId,  // $1
          id                    // $2
        );
      } else if (flg === 'delCalendar') {
        // カレンダー削除
        updateResult = await schemaPrisma.$executeRawUnsafe(`
          DELETE FROM jcpotw_group_table WHERE id = $1::uuid;
        `,
          id          // $1
        );
      } else {
        // カレンダー削除と個別設定解除以外の場合がない
      }
      return { success: !(updateResult === 0 && flg !== 'delCalendar') };
    } catch (error) {
      handleServerError(error);
    }
  }

  // 個別設定コピー
  @LogFunctionSignature()
  static async copyCalendarIniData(
    selectedCalendarId: string,
    selectedItemIds: any,
  ) {
    const session = await getIronSession<SessionData>(cookies(), sessionOptions);

    // データベースにリンクする文字列を動的に生成する
    const databaseUrl = `postgresql://${session.user.schemaUser}:${session.user.schemaPwd}@${ENV.PGSQL_HOST}:${ENV.PGSQL_PORT}/${ENV.PGSQL_DATABASE_WORKFLOW}`;
    const schemaPrisma = getPrismaClient(databaseUrl);

    try {
      await schemaPrisma.$transaction(async (tx) => {
        // コピー元の ini_dat を取得
        const ini_dat = await tx.$queryRawUnsafe<any>(
          `
          SELECT
            JGT.ini_dat
          FROM
            jcpotw_group_table JGT 
          WHERE
            id = $1::uuid;
          `,
          selectedCalendarId // $1 - コピー元
        );

        if (!ini_dat || ini_dat.length === 0 || !ini_dat[0].ini_dat) {
          // データがない場合、エラーを投げてロールバック
          throw new Error(`コピー元[${selectedCalendarId}]が存在しないため、ロールバックする。`);
        }

        const iniDatValue = ini_dat[0].ini_dat; // ini_dat の最初の要素を取得

        // `JCPOTW_GROUP_TABLE` テーブルをバッチ更新する SQL クエリを実行
        const result = await tx.$executeRawUnsafe(
          `
          UPDATE 
              jcpotw_group_table
          SET 
              ini_dat = $1,
              client_config_update = NOW() AT TIME ZONE 'UTC',
              update_date = NOW() AT TIME ZONE 'UTC',
              update_user = $2
          WHERE 
              id = ANY($3::uuid[]);
          `,
          iniDatValue, // $1 - コピー元の ini_dat
          session.user.userId, // $2 - 更新ユーザー
          selectedItemIds // $3 - コピー先の UUID リスト
        );

        if (result !== selectedItemIds.length) {
          throw new Error(
            // 一部データが更新失敗の場合、エラーを投げてロールバック
            `${selectedItemIds.length - result} 件のコピー先が存在しないため、ロールバックする。`
          );
        }
      });

      return true; // すべて成功した場合
    } catch (error) {
      // トランザクションが失敗し、ロールバックされました
      Logger.info({
        message: (error as Error).message,
      });
      return false;
    }
  }

  // カレンダーグループ情報の新規と変更
  // @LogFunctionSignature() ファイルの内容が多いので、該当方法のパラメータをログに出力しない
  static async insertOrUpdateCalendar(
    id: string,
    timeCalContents: Buffer,
    extCalARContents: Buffer,
    calendarToolName: string,
    calendarToolContents: Buffer,
    groupName: string,
    holidayCalContents: Buffer,
  ) {
    // セッション情報を取得
    const session = await getIronSession<SessionData>(cookies(), sessionOptions);

    // データベースにリンクする文字列を動的に生成する
    const databaseUrl = `postgresql://${session.user.schemaUser}:${session.user.schemaPwd}@${ENV.PGSQL_HOST}:${ENV.PGSQL_PORT}/${ENV.PGSQL_DATABASE_WORKFLOW}`;
    const schemaPrisma = getPrismaClient(databaseUrl);
    let updateResult;
    // カレンダーグループ情報変更場合
    if (id && id.length > 0) {
      // カレンダーグループ情報の変更
      updateResult = await schemaPrisma.$executeRawUnsafe(`
      UPDATE 
        jcpotw_group_table 
      SET
        time_cal = $1,
        ext_cal_ar = $2,
        calendar_tool_name = $3, 
        calendar_tool_data = $4,
        upload_date = NOW() AT TIME ZONE 'UTC',
        update_date = NOW() AT TIME ZONE 'UTC',
        update_user = $5,
        enabled_flag = '1'
      WHERE
        id = $6::uuid
    `,
        timeCalContents,               // $1 - TIME_CAL
        extCalARContents,              // $2 - EXT_CAL_AR
        calendarToolName,              // $3 - CALENDAR_TOOL_NAME
        calendarToolContents,          // $4 - CALENDAR_TOOL_DATA
        session.user.userId,           // $5 - UPDATE_USER
        id                             // $6 - ID
      );
    } else {
      // カレンダーグループ情報の登録
      updateResult = await schemaPrisma.$executeRawUnsafe(`
      INSERT INTO jcpotw_group_table (
        id,
        group_name, time_cal, holiday_cal, ext_cal_ar, 
        calendar_tool_name, calendar_tool_data, upload_date, 
        update_date, update_user, enabled_flag
      ) VALUES (
        $1::uuid, $2, $3, $4, $5, $6, $7, NOW() AT TIME ZONE 'UTC', NOW() AT TIME ZONE 'UTC', $8, B'1'
      )
    `,
        uuidv4(),                      // $1 - ID
        sanitizeHtml(groupName),       // $2 - GROUP_NAME
        timeCalContents,               // $3 - TIME_CAL
        holidayCalContents,            // $4 - HOLIDAY_CAL
        extCalARContents,              // $5 - EXT_CAL_AR
        calendarToolName,              // $6 - CALENDAR_TOOL_NAME
        calendarToolContents,          // $7 - CALENDAR_TOOL_DATA
        session.user.userId            // $8 - UPDATE_USER
      );
    }
    return updateResult;
  }

  // カレンダーグループの個別設定overtime.iniデータの変更
  // @LogFunctionSignature() ファイルの内容が多いので、該当方法のパラメータをログに出力しない
  static async updateCalendarIniData(
    iniContents: Buffer,
    id: string,
  ) {
    const session = await getIronSession<SessionData>(cookies(), sessionOptions);

    // データベース接続文字列を動的に生成する
    const databaseUrl = `postgresql://${session.user.schemaUser}:${session.user.schemaPwd}@${ENV.PGSQL_HOST}:${ENV.PGSQL_PORT}/${ENV.PGSQL_DATABASE_WORKFLOW}`;
    const schemaPrisma = getPrismaClient(databaseUrl);

    // データベースのテーブルを更新
    const updateResult = await schemaPrisma.$executeRawUnsafe(`
      UPDATE 
        jcpotw_group_table
      SET 
        ini_dat = $1
        , client_config_update = NOW() AT TIME ZONE 'UTC'
        , update_date = NOW() AT TIME ZONE 'UTC'
        , update_user = $2
      WHERE id = $3::uuid;
    `,
      iniContents,          // $1: 個別設定overtime.iniデータ
      session.user.userId,  // $2: UPDATE_USER
      id,                   // $3: UUID
    );
    return updateResult;
  }


  // カレンダーグループ名により、カレンダーを取得するメソッド
  @LogFunctionSignature()
  static async getCalendarsByGrpname(
    grpName: string
  ) {
    try {
      const session = await getIronSession<SessionData>(cookies(), sessionOptions);

      // データベースにリンクする文字列を動的に生成する
      const databaseUrl = `postgresql://${session.user.schemaUser}:${session.user.schemaPwd}@${ENV.PGSQL_HOST}:${ENV.PGSQL_PORT}/${ENV.PGSQL_DATABASE_WORKFLOW}`;
      const schemaPrisma = getPrismaClient(databaseUrl);

      const calendars = await schemaPrisma.$queryRawUnsafe<any[]>(`
        SELECT
            JGT.id 
        FROM
            jcpotw_group_table JGT 
        WHERE JGT.group_name = $1;
        `,
        grpName  // プレースホルダ$1にグループ名を渡す
      );
      const uperCalendars = calendars.map(toUperCaseKeys);
      return uperCalendars;

    } catch (error) {
      handleServerError(error);
    }
  }
  // #endregion カレンダー
  // #endregion PC自動シャットダウン
}

export default ServerData;
