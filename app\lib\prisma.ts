/**
 * @file prisma.ts
 * @description
 * <AUTHOR>
 * @copyright Copyright (C) 2025, Hitachi Solutions, Ltd.
 */

import Logger from "@/app/lib/logger";
import { Prisma, PrismaClient } from "@prisma/client";

// グローバルでprismaを宣言
declare global {
  var prisma:
    | PrismaClient<
        Prisma.PrismaClientOptions,
        "info" | "warn" | "error" | "query"
      >
    | undefined;
}

// グローバルでprismaが未定義の場合は新しいPrismaClientを作成
const prisma =
  global.prisma ||
  new PrismaClient({
    log: [
      {
        emit: "event",
        level: "query",
      },
    ],
  });

// クエリイベントが発生したときにログを出力
prisma.$on("query", (e: Prisma.QueryEvent) => {
  Logger.debug({
    message: "prisma:query",
    query: e.query,
    params: e.params,
    duration: e.duration,
  });
});

// 開発環境の場合はグローバルでprismaを再設定
if (process.env.NODE_ENV === "development") global.prisma = prisma;

export default prisma;
